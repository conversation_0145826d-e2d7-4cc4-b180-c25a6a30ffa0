'use client';
import { useMemo } from 'react';
import { cn } from '../../../../lib/utils';
import { useIsMobile } from '../../../../hooks/use-mobile';
import { useSharedSidebarState } from '../../../../stores/sidebar-store.client';
import { SquircleProvider } from '../../../providers/squircle-provider.client';
import {
  SidebarProvider,
  SidebarInset,
} from '../../../ui/sidebar.client';
import { StudentTopNav } from './student-top-nav.client';
import { StudentUserAvatar } from './student-user-avatar.client';

interface EnhancedSidebarStudentProps {
  children: React.ReactNode;
  className?: string;
  showUserAvatar?: boolean;
  showTopNav?: boolean;
  enableRightSidebar?: boolean; // Keep option for future chat interface
}

export function EnhancedSidebarStudent({
  children,
  showUserAvatar = true,
  showTopNav = true,
  enableRightSidebar = false // Keep option for future chat interface
}: EnhancedSidebarStudentProps) {
  const isMobile = useIsMobile();
  const {
    activeFlyout,
    updateActiveFlyout,
  } = useSharedSidebarState();

  // Calculate main content styles based on flyout states - FASTER TRANSITIONS
  const mainContentStyles = useMemo(() => {
    const styles: Record<string, string> = {
      "transition-all": "true",
      "duration-100": "true", // FASTER: 100ms instead of 300ms
      "ease-out": "true", // FASTER: ease-out instead of ease-in-out
      "flex-1": "true",
      "h-screen": "true",
    };

    // Left flyout adjustments - only when hamburger menu flyout is open
    if (!isMobile && activeFlyout) {
      styles["ml-[284px]"] = "true"; // Flyout width (284px)
    }

    return cn(styles);
  }, [isMobile, activeFlyout]);

  // Top navigation styles calculation - FASTER TRANSITIONS
  const topNavStyles = useMemo(() => {
    const styles: Record<string, string> = {
      "fixed": "true",
      "top-0": "true",
      "left-0": "true",
      "right-0": "true",
      "h-16": "true",
      "z-50": "true",
      "transition-all": "true",
      "duration-100": "true", // FASTER: 100ms instead of 200ms
      "ease-out": "true",
      "border-b": "true",
      "border-black/10": "true",
      "dark:border-white/10": "true",
    };

    // Left flyout adjustments
    if (!isMobile && activeFlyout) {
      styles["ml-[284px]"] = "true"; // Flyout width (284px)
    }

    return cn(styles);
  }, [isMobile, activeFlyout]);

  return (
    <SquircleProvider>
      
        <SidebarProvider
          defaultOpenLeft={false}
          defaultOpenRight={false}
          className="min-h-screen bg-fixed bg-gradient-to-r from-[#f2f2f2] via-[#e8e8e8] to-[#eeeeee] dark:from-[#101012] dark:via-[#18181a] dark:to-[#171719]"
        >
        <div className="flex h-screen w-full">
          {/* Top Navigation - Re-enabled after identifying real issue */}
          {showTopNav && (
            <div className={topNavStyles}>
              <StudentTopNav
                flyout={activeFlyout}
                setFlyout={updateActiveFlyout}
              />
            </div>
          )}

          {/* Student User Avatar - Simple top-right avatar with popover */}
          {showUserAvatar && (
            <StudentUserAvatar />
          )}

          {/* Main Content Area - Single scrollable container */}
          <SidebarInset className={cn("flex-1 rounded-b-xl", mainContentStyles)}>
            <main className="pt-16 overflow-y-auto custom-scrollbar h-full">
              {children}
            </main>
          </SidebarInset>

          {/* Future: Right Sidebar for chat interface */}
          {enableRightSidebar && (
            <div className="hidden">
              {/* Placeholder for future chat interface */}
            </div>
          )}
        </div>
        </SidebarProvider>
      
    </SquircleProvider>
  );
}
