// triggers/session.ts
import type { AddTrigger, SetTrigger, RemoveTrigger } from 'blade/types';

// Trigger for creating sessions
export const add: AddTrigger = (query) => {
  // Ensure query.with exists
  if (!query.with) {
    return query;
  }

  // Handle both single object and array cases
  const processSessionData = (sessionData: any) => {
    // Set default timestamps
    sessionData.createdAt = new Date();
    sessionData.updatedAt = new Date();

    // Set default expiration (24 hours from now)
    if (!sessionData.expiresAt) {
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);
      sessionData.expiresAt = expiresAt;
    }

    return sessionData;
  };

  // Handle array of sessions
  if (Array.isArray(query.with)) {
    query.with = query.with.map(processSessionData);
  } else {
    // Handle single session
    query.with = processSessionData(query.with);
  }

  return query;
};

// Trigger for updating sessions
export const set: SetTrigger = (query) => {
  // Ensure query.to exists
  if (!query.to) {
    return query;
  }

  // Update timestamp
  (query.to as any)['updatedAt'] = new Date();

  return query;
};

// Trigger for removing sessions (cleanup)
export const remove: RemoveTrigger = (query) => {
  // Add any cleanup logic here if needed
  return query;
};