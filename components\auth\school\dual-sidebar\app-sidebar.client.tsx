'use client';
import { ComponentProps, useState, useCallback } from "react";
import { motion, AnimatePresence } from 'motion/react';
import { cn } from "../../../../lib/utils";
import { useIsMobile } from "../../../../hooks/use-mobile";
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarFooter,
} from "../../../ui/sidebar.client";
import { <PERSON><PERSON>, <PERSON>etContent, SheetTitle } from "../../../ui/sheet.client";
import { NavMain } from "./nav-main.client";
import { NavProjects } from "./nav-projects.client";
import { NavUser } from "./nav-user.client";
import { TeamSwitcher } from "./team-switcher.client";
import {
  Bell,
  FolderOpen,
  Plus,
  Compass,
  PlayCircle,
  GalleryVerticalEnd,
  SquareTerminal,
  Bot,
  BookOpen,
  Settings2,
  Frame,
  Pie<PERSON>,
  Map,
} from "lucide-react";
import { <PERSON>, Image } from 'blade/client/components';


interface AppSidebarProps extends ComponentProps<"div"> {
  flyout: string | null;
  setFlyout: (flyout: string | null) => void;
  variant?: "sidebar" | "floating" | "inset";
  collapsible?: "offcanvas" | "icon" | "none";
}

const iconItems = [
  // First section - Notifications
  {
    id: "notifications",
    icon: Bell,
    label: "Notifications",
    tooltip: "Notifications",
    section: 1
  },
  // Second section - Navigation
  {
    id: "projects",
    icon: FolderOpen,
    label: "Projects",
    tooltip: "Your Projects",
    section: 2
  },
  {
    id: "runs",
    icon: PlayCircle,
    label: "Runs",
    tooltip: "Your Runs",
    section: 2
  },
  {
    id: "discover",
    icon: Compass,
    label: "Discover",
    tooltip: "Discover",
    section: 2
  },
  // Third section - Create New
  {
    id: "new",
    icon: Plus,
    label: "New",
    tooltip: "Create New",
    section: 3
  },
];

function IconButton({ 
  item, 
  isActive, 
  onClick,
  isMobile = false
}: { 
  item: typeof iconItems[number];
  isActive: boolean;
  onClick: () => void;
  isMobile?: boolean;
}) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className="relative group">
      <button
        onClick={onClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={cn(
          "relative w-12 h-12 flex items-center justify-center rounded-xl transition-all duration-300 ease-out transform-gpu will-change-transform overflow-hidden",
          "z-20 touch-manipulation select-none outline-none"
        )}
        style={{
          transform: isActive ? 'scale(1.05)' : isHovered ? 'scale(1.02)' : 'scale(1)',
          WebkitTapHighlightColor: 'transparent',
          WebkitTouchCallout: 'none',
          WebkitUserSelect: 'none',
          userSelect: 'none',
          touchAction: 'manipulation',
          cursor: 'pointer',
          pointerEvents: 'auto'
        }}
        type="button"
        aria-pressed={isActive}
        aria-label={item.tooltip}
      >
        {/* Main glassmorphism background */}
        <div 
          className="absolute inset-0 rounded-xl transition-all duration-300 ease-out backdrop-blur-[32px] backdrop-saturate-[180%]"
          style={{
            background: isActive 
              ? `
                linear-gradient(135deg, 
                  rgba(0, 0, 0, 0.12) 0%,
                  rgba(0, 0, 0, 0.08) 30%,
                  rgba(0, 0, 0, 0.04) 70%,
                  rgba(0, 0, 0, 0.02) 100%
                ),
                radial-gradient(circle at 30% 30%, 
                  rgba(0, 0, 0, 0.15) 0%, 
                  transparent 70%
                )
              `
              : isHovered
              ? `
                linear-gradient(135deg, 
                  rgba(0, 0, 0, 0.06) 0%,
                  rgba(0, 0, 0, 0.03) 50%,
                  rgba(0, 0, 0, 0.01) 100%
                )
              `
              : 'transparent',
            border: isActive 
              ? '1px solid rgba(0, 0, 0, 0.1)'
              : isHovered 
              ? '1px solid rgba(0, 0, 0, 0.05)'
              : '1px solid transparent'
          }}
        />
        
        {/* Icon with enhanced styling */}
        <item.icon 
          className={cn(
            "w-5 h-5 relative z-10 transition-all duration-300 ease-out",
            isActive 
              ? "text-blue dark:text-white drop-shadow-[0_2px_8px_rgba(0,0,0,0.4)] dark:drop-shadow-[0_2px_8px_rgba(255,255,255,0.4)] scale-110" 
              : isHovered
              ? "text-black/90 dark:text-white/90 scale-105"
              : "text-black/60 dark:text-white/60"
          )}
        />
      </button>

      {/* Enhanced active indicator */}
      <div 
        className="absolute -right-1 top-[28px] transform -translate-y-1/2 transition-all duration-300 ease-out z-20"
        style={{
          opacity: isActive ? 1 : 0,
          transform: `translateY(-50%) scale(${isActive ? 1 : 0.8})`,
        }}
      >
        <div 
          className="w-1.5 h-1.5 rounded-full bg-black dark:bg-white"
          style={{
            boxShadow: '0 0 12px rgba(0, 0, 0, 0.6), 0 0 24px rgba(0, 0, 0, 0.3)',
            filter: 'blur(0.5px)',
          }}
        />
      </div>
      
      {/* Hover tooltip */}
      <div
        className="absolute left-16 top-[2.5rem] font-manrope_1 transform -translate-y-1/2 bg-gradient-to-b dark:from-[#f8f9fa] dark:via-[#f8f9fa] dark:to-[#e9ecef] from-[#212026] via-[#212026] to-[#29282e] px-3 text-sm leading-8 text-white/80 dark:text-black/80 dark:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(226_232_240)_inset,0_0.5px_0_1.5px_#64748b_inset] shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] rounded-lg pointer-events-none transition-all duration-300 ease-out whitespace-nowrap z-50"
        style={{
          opacity: isHovered && !isActive ? 1 : 0,
          transform: `translate(${isHovered && !isActive ? '0' : '-8px'}, -50%)`,
          backdropFilter: 'blur(10px)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        }}
      >
        {item.tooltip}
        <div 
          className="absolute left-0 top-1/2 -translate-x-1 -translate-y-1/2 w-0 h-0 dark:border-r-[#e9ecef] border-r-[#212026]"
          style={{
            borderTop: '6px solid transparent',
            borderBottom: '6px solid transparent',
            borderRightWidth: '6px',
            borderRightStyle: 'solid',
          }}
        />
      </div>
    </div>
  );
}
// Add a Separator component
function Separator() {
  return (
    <div className="w-8 h-px bg-black/10 dark:bg-white/10 my-2 mx-auto" />
  );
}

export function AppSidebar({
  flyout,
  setFlyout,
  variant = "sidebar",
  collapsible = "none",
  className,
  ...props
}: AppSidebarProps) {
  const [activeItem, setActiveItem] = useState<string | null>("projects");
  const isMobile = useIsMobile();

  // Simplified toggle handler without debouncing - matches working right-sidebar pattern
  const handleToggleFlyout = useCallback((itemId: string) => {
    console.log(`🎯 Toggle flyout for ${itemId}:`, { currentFlyout: flyout, itemId });
    const newFlyout = flyout === itemId ? null : itemId;
    console.log(`🎯 Setting flyout to:`, newFlyout);
    setFlyout(newFlyout);
  }, [flyout, setFlyout]);

  const data = {
    user: {
      name: "School Admin",
      email: "<EMAIL>",
      avatar: "/avatars/admin.jpg",
    },
    teams: [
      {
        name: "School District",
        logo: GalleryVerticalEnd,
        plan: "Education",
      },
    ],
    navMain: [
      {
        title: "Dashboard",
        url: "#",
        icon: SquareTerminal,
        isActive: true,
        items: [
          {
            title: "Overview",
            url: "#",
          },
          {
            title: "Analytics",
            url: "#",
          },
          {
            title: "Reports",
            url: "#",
          },
        ],
      },
      {
        title: "Students",
        url: "#",
        icon: Bot,
        items: [
          {
            title: "All Students",
            url: "#",
          },
          {
            title: "Enrollment",
            url: "#",
          },
          {
            title: "Attendance",
            url: "#",
          },
        ],
      },
      {
        title: "Teachers",
        url: "#",
        icon: BookOpen,
        items: [
          {
            title: "All Teachers",
            url: "#",
          },
          {
            title: "Schedules",
            url: "#",
          },
          {
            title: "Performance",
            url: "#",
          },
        ],
      },
      {
        title: "Settings",
        url: "#",
        icon: Settings2,
        items: [
          {
            title: "General",
            url: "#",
          },
          {
            title: "Users",
            url: "#",
          },
          {
            title: "Security",
            url: "#",
          },
        ],
      },
    ],
    projects: [
      {
        name: "Academic Year 2024",
        url: "#",
        icon: Frame,
      },
      {
        name: "Summer Programs",
        url: "#",
        icon: PieChart,
      },
      {
        name: "Extracurriculars",
        url: "#",
        icon: Map,
      },
    ],
  };

  if (collapsible === "icon") {
    return (
      <div className="flex h-full">
        {/* Icon Rail */}
        <div className="flex flex-col w-18 h-svh z-30 fixed left-0 top-0 border-r border-black/10 dark:border-white/10 bg-[#f2f2f2] dark:bg-[#0d0d0d]">
          <div className="flex flex-col items-center gap-2 p-2">
            {/* Logo Section */}
            <div className="w-10 h-10 flex items-center justify-center  z-20">
              <Image
                    src="/logo-lightmode.png"
                    alt="Penned Logo"
                    className="w-6 h-6 dark:hidden"
                    width={28}
                    height={28}
                  />
                  {/* Dark mode logo - shows in dark mode */}
                  <Image
                    src="/logo-darkmode.png"
                    alt="Penned Logo"
                    className="w-6 h-6 hidden dark:block"
                    width={28}
                    height={28}
                  />
                            
                            </div>

            {/* Bell Icon */}
            <IconButton
              item={iconItems[0]}
              isActive={flyout === iconItems[0].id}
              onClick={() => handleToggleFlyout(iconItems[0].id)}
              isMobile={isMobile}
            />
            
            <Separator />
            
            {/* Navigation Section */}
            {iconItems
              .filter(item => item.section === 2)
              .map((item) => (
                <IconButton
                  key={item.id}
                  item={item}
                  isActive={flyout === item.id}
                  onClick={() => handleToggleFlyout(item.id)}
                  isMobile={isMobile}
                />
              ))}
            
            <Separator />
            
            {/* Create New Section */}
            {iconItems
              .filter(item => item.section === 3)
              .map((item) => (
                <IconButton
                  key={item.id}
                  item={item}
                  isActive={flyout === item.id}
                  onClick={() => handleToggleFlyout(item.id)}
                  isMobile={isMobile}
                />
              ))}
          </div>
        </div>

        {/* Flyout Panel */}
        {isMobile ? (
          <Sheet open={!!flyout} onOpenChange={(open) => !open && setFlyout(null)} modal={false}>
            <SheetContent
              side="left"
              className={cn(
                "mobile-sheet-content",
                "border-r  border-black/5 dark:border-white/5 p-0 bg-[#f2f2f2] dark:bg-[#0d0d0d]",
                "fixed left-[72px] top-0 h-full w-[calc(100vw-72px)] max-w-none",
                "z-10"
              )}
            >
              <SheetTitle className="sr-only">
                {flyout === 'projects' ? 'Projects' :
                 flyout === 'runs' ? 'Runs' :
                 flyout === 'discover' ? 'Discover' :
                 flyout === 'new' ? 'Create New' :
                 flyout === 'notifications' ? 'Notifications' :
                 'Navigation Panel'}
              </SheetTitle>
              <div className="h-full overflow-hidden">
                {renderFlyoutContent(flyout, data)}
              </div>
            </SheetContent>
          </Sheet>
        ) : (
          <>
            {/* Flyout separator line */}
            <AnimatePresence>
              {flyout && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30, mass: 0.8 }}
                  className="fixed left-[71px] top-0 h-svh w-[1px] bg-[#f2f2f2] dark:bg-[#0d0d0d] z-10"
                />
              )}
            </AnimatePresence>

            {/* Flyout content panel */}
            <AnimatePresence mode="wait">
              {flyout && (
                <motion.div
                  initial={{ opacity: 0, x: -100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30, mass: 0.8 }}
                  className="fixed left-[72px] top-0 h-svh w-[278px] bg-[#f2f2f2] dark:bg-[#0d0d0d] border-r border-black/5 dark:border-white/5 shadow-xl z-10 flex flex-col"
                >
                  {renderFlyoutContent(flyout, data)}
                </motion.div>
              )}
            </AnimatePresence>
          </>
        )}
      </div>
    );
  }

  // Regular sidebar for non-icon modes
  return (
    <Sidebar
      side="left"
      variant={variant}
      collapsible={collapsible}
      className={cn("border-r", className)}
      {...props}
    >
      <SidebarHeader className="border-b">
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}

function renderFlyoutContent(flyout: string | null, data: any) {
  if (!flyout) return null;
  
  switch (flyout) {
    case "notifications":
      return (
        <div className="flex py-2 flex-col h-full">
          <div className="p-2 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-semibold text-black dark:text-white">
              Notifications
            </h2>
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-2">
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">New student enrollment</p>
                <p className="text-xs text-muted-foreground">2 hours ago</p>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">Teacher schedule update</p>
                <p className="text-xs text-muted-foreground">Yesterday</p>
              </div>
            </div>
          </div>
        </div>
      );
    
    case "runs":
      return (
        <div className="flex py-2 flex-col h-full">
      <div className="p-2 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-semibold text-black dark:text-white">
              Recent Activities
            </h2>          
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-2">
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">Grade Reports Generated</p>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">Attendance Sync</p>
              </div>
            </div>
          </div>
        </div>
      );

    case "discover":
      return (
        <div className="flex py-2 flex-col h-full">
         <div className="p-2 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-semibold text-black dark:text-white">
              Discover
            </h2>          
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-2">
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">New Features</p>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">Training Resources</p>
              </div>
            </div>
          </div>
        </div>
      );

    case "new":
      return (
        <div className="flex py-2 flex-col h-full">
          <div className="p-2 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-semibold text-black dark:text-white">
              Create New  
            </h2>          
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-2">
              <button className="w-full text-left p-2 rounded-md hover:bg-sidebar-accent">
                New Student
              </button>
              <button className="w-full text-left p-2 rounded-md hover:bg-sidebar-accent">
                New Teacher
              </button>
              <button className="w-full text-left p-2 rounded-md hover:bg-sidebar-accent">
                New Class
              </button>
            </div>
          </div>
        </div>
      );

    case "projects":
      return (
        <div className="flex py-2 flex-col h-full">
          <div className="p-4 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-semibold text-black dark:text-white">
              Projects
            </h2>
          </div>
          <div className="flex-1 overflow-auto">
            <NavProjects
              projects={data.projects}
              showCreateButton={true}
              limit={20}
            />
          </div>
        </div>
      );

    default:
      return (
        <div className="flex items-center justify-center h-full">
          <p className="text-muted-foreground">Select a panel</p>
        </div>
      );
  }
}