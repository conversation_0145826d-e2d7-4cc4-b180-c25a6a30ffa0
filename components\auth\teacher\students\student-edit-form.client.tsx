'use client';

import { useState } from 'react';
import { useMutation } from 'blade/client/hooks';
import { Dialog } from '@base-ui-components/react/dialog';
import { User, X } from 'lucide-react';
import { cn } from '../../../../lib/utils';

interface Student {
  id: string;
  name: string;
  email: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  teacherId?: string;
  createdAt?: string;
}

interface StudentEditFormProps {
  student: Student;
  isOpen: boolean;
  onClose: () => void;
}

export default function StudentEditForm({ student, isOpen, onClose }: StudentEditFormProps) {
  const [formData, setFormData] = useState({
    name: student.name || '',
    email: student.email || '',
    username: student.username || '',
    grade: student.grade || '',
    isActive: student.isActive ?? true
  });
  const [isLoading, setIsLoading] = useState(false);
  const { set } = useMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await set.users({
        with: { id: student.id },
        to: {
          name: formData.name,
          email: formData.email,
          username: formData.username || null,
          grade: formData.grade || null,
          isActive: formData.isActive
        }
      });

      onClose();
    } catch (error) {
      console.error('Error updating student:', error);
      alert('Failed to update student. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Backdrop className="fixed inset-0 bg-black/50 opacity-100 transition-all duration-150 data-[ending-style]:opacity-0 data-[starting-style]:opacity-0 z-[100000]" />
        <Dialog.Popup className="fixed top-1/2 left-1/2 -mt-8 w-[500px] max-w-[calc(100vw-3rem)] -translate-x-1/2 -translate-y-1/2 rounded-lg bg-white dark:bg-gray-800 p-6 text-gray-900 dark:text-gray-100 outline outline-1 outline-gray-200 dark:outline-gray-700 transition-all duration-150 data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 z-[100001]">
          <Dialog.Title className="-mt-1.5 mb-1 text-lg font-medium flex items-center gap-2">
            <User className="w-5 h-5" />
            Edit Student: {student.name}
          </Dialog.Title>
          <Dialog.Description className="mb-6 text-base text-gray-600 dark:text-gray-400">
            Update student information and settings.
          </Dialog.Description>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="edit-student-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Student Name
              </label>
              <input
                id="edit-student-name"
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Enter student's full name"
                required
                disabled={isLoading}
              />
            </div>

            <div>
              <label htmlFor="edit-student-email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email Address
              </label>
              <input
                id="edit-student-email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="<EMAIL>"
                required
                disabled={isLoading}
              />
            </div>

            <div>
              <label htmlFor="edit-student-username" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Username (for login)
              </label>
              <input
                id="edit-student-username"
                type="text"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="student_username"
                disabled={isLoading}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Students use their username to log in (no password required)
              </p>
            </div>

            <div>
              <label htmlFor="edit-student-grade" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Grade Level
              </label>
              <select
                id="edit-student-grade"
                value={formData.grade}
                onChange={(e) => handleInputChange('grade', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                disabled={isLoading}
              >
                <option value="">Select grade level</option>
                <option value="9">9th Grade</option>
                <option value="10">10th Grade</option>
                <option value="11">11th Grade</option>
                <option value="12">12th Grade</option>
              </select>
            </div>

            <div className="flex items-center">
              <input
                id="edit-student-active"
                type="checkbox"
                checked={formData.isActive}
                onChange={(e) => handleInputChange('isActive', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                disabled={isLoading}
              />
              <label htmlFor="edit-student-active" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Student is active
              </label>
            </div>

            <div className="flex justify-end gap-4 pt-4">
              <Dialog.Close 
                className="flex h-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 px-3.5 text-base font-medium text-gray-900 dark:text-gray-100 select-none hover:bg-gray-100 dark:hover:bg-gray-600 focus-visible:outline focus-visible:outline-2 focus-visible:-outline-offset-1 focus-visible:outline-blue-800 active:bg-gray-100 dark:active:bg-gray-600"
                disabled={isLoading}
              >
                Cancel
              </Dialog.Close>
              <button
                type="submit"
                disabled={isLoading}
                className={cn(
                  "flex h-10 items-center justify-center rounded-md px-3.5 text-base font-medium text-white select-none focus-visible:outline focus-visible:outline-2 focus-visible:-outline-offset-1 focus-visible:outline-blue-800",
                  isLoading
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-blue-600 hover:bg-blue-700 active:bg-blue-700"
                )}
              >
                {isLoading ? 'Updating...' : 'Update Student'}
              </button>
            </div>
          </form>

          {/* Close button in top right */}
          <Dialog.Close className="absolute top-4 right-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:outline-none disabled:pointer-events-none">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Dialog.Close>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
