# Dynamic Sidebar System

A comprehensive, configurable sidebar system for Blade applications that adapts to different user roles and preferences. This system provides a flexible alternative to creating separate layouts for each user type.

## Features

### 🎯 Role-Based Configuration
- **Student View**: Read-only access with course-focused navigation
- **Teacher View**: Create/edit capabilities with class management tools
- **School Admin View**: Full system administration with analytics and user management
- Automatic permission filtering based on user roles

### 🔧 Flexible Action Types
- **Navigate**: Direct page navigation using Blade router
- **Flyout**: Expandable sidebar panels with additional content
- **Modal**: Popup dialogs for forms and detailed actions
- **Inline**: Content displayed directly within the sidebar

### 📱 Responsive Design
- Mobile-first approach with Sheet overlays on small screens
- Desktop flyout panels for enhanced productivity
- Touch-friendly interactions and smooth animations
- Adaptive spacing and margins based on sidebar states

### ⚙️ Customizable Layout Modes
- **Dual Sidebar**: Left navigation + right content panels
- **Left-Only**: Traditional left sidebar navigation
- **Right-Only**: Right-side content panels only
- **Hidden**: Full-screen mode without sidebars

### 🎨 Enhanced UX
- Glassmorphism effects with backdrop blur
- Smooth hover animations and scaling effects
- Badge notifications for important items
- Tooltip guidance for icon-only modes
- Persistent state using Blade cookies

## Quick Start

### Basic Implementation

```tsx
import { DynamicLayoutWrapper } from '../components/auth/dynamic-sidebar';

export default function MyPage() {
  return (
    <DynamicLayoutWrapper userRole="student">
      <div className="p-6">
        <h1>My Page Content</h1>
      </div>
    </DynamicLayoutWrapper>
  );
}
```

### With Custom Configuration

```tsx
import { DynamicLayoutWrapper } from '../components/auth/dynamic-sidebar';

export default function MyPage() {
  const customConfig = {
    mode: 'left-only' as const,
    leftSidebar: {
      width: 'compact' as const,
      allowFlyouts: false
    }
  };

  return (
    <DynamicLayoutWrapper 
      userRole="teacher"
      customConfig={customConfig}
    >
      <div className="p-6">
        <h1>My Page Content</h1>
      </div>
    </DynamicLayoutWrapper>
  );
}
```

### With Action Handling

```tsx
import { DynamicLayoutWrapper } from '../components/auth/dynamic-sidebar';
import { SidebarAction, SidebarItem } from '../types/sidebar-config';

export default function MyPage() {
  const handleAction = (action: SidebarAction, item: SidebarItem) => {
    switch (action.type) {
      case 'navigate':
        // Handle navigation
        break;
      case 'modal':
        // Open modal dialogs
        if (action.target === 'create-assignment') {
          openCreateAssignmentModal();
        }
        break;
      case 'flyout':
        // Flyout content is handled automatically
        break;
    }
  };

  return (
    <DynamicLayoutWrapper 
      userRole="teacher"
      onAction={handleAction}
    >
      <div className="p-6">
        <h1>My Page Content</h1>
      </div>
    </DynamicLayoutWrapper>
  );
}
```

## Configuration

### Sidebar Configuration Structure

```typescript
interface SidebarConfig {
  mode: 'dual' | 'left-only' | 'right-only' | 'hidden';
  leftSidebar?: {
    sections: SidebarSection[];
    width?: 'icon' | 'compact' | 'full';
    allowFlyouts?: boolean;
  };
  rightSidebar?: {
    sections: SidebarSection[];
    defaultContent?: string;
    allowFlyouts?: boolean;
  };
  userNav?: {
    show: boolean;
    position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    items: SidebarItem[];
  };
  header?: {
    show: boolean;
    title?: string;
    actions?: SidebarItem[];
  };
  permissions: {
    canCreate?: boolean;
    canEdit?: boolean;
    canDelete?: boolean;
    canView?: boolean;
  };
}
```

### Creating Custom Configurations

1. **Modify existing configurations** in `config/sidebar-configs.ts`
2. **Create new role configurations** by adding to the `roleConfigs` object
3. **Add custom sidebar items** with icons, actions, and permissions
4. **Define new action types** and handle them in your components

### Example Custom Configuration

```typescript
const customTeacherConfig: SidebarConfig = {
  mode: 'dual',
  leftSidebar: {
    width: 'icon',
    allowFlyouts: true,
    sections: [
      {
        id: 'main',
        items: [
          {
            id: 'dashboard',
            label: 'Dashboard',
            icon: Home,
            action: { type: 'navigate', target: '/teacher/dashboard' }
          },
          {
            id: 'my-classes',
            label: 'My Classes',
            icon: GraduationCap,
            action: { type: 'flyout', target: 'classes' },
            badge: 4
          }
        ]
      }
    ]
  },
  rightSidebar: {
    allowFlyouts: true,
    defaultContent: 'quick-actions',
    sections: [
      {
        id: 'quick-actions',
        title: 'Quick Actions',
        items: [
          {
            id: 'create-assignment',
            label: 'Create Assignment',
            icon: Plus,
            action: { type: 'modal', target: 'create-assignment' }
          }
        ]
      }
    ]
  },
  permissions: {
    canView: true,
    canCreate: true,
    canEdit: true,
    canDelete: false
  }
};
```

## Components

### Core Components

- **DynamicSidebar**: Main sidebar component with full configuration
- **DynamicLayoutWrapper**: Easy-to-use wrapper with role-based defaults
- **DynamicLeftSidebar**: Left sidebar with icon mode and flyouts
- **DynamicRightSidebar**: Right sidebar with content panels and flyouts
- **DynamicUserNav**: User navigation with avatar and quick actions
- **DynamicHeader**: Configurable header with title and actions

### State Management

The system uses the existing `useSidebarState` hook for:
- Persistent sidebar states using Blade cookies
- Instant UI updates with background persistence
- Flyout state management
- Active item tracking

## Styling and Theming

### CSS Classes

The system uses Tailwind CSS with custom classes for:
- Glassmorphism effects: `bg-[#1a1a1a]/95 backdrop-blur-xl`
- Smooth transitions: `transition-all duration-200 ease-out`
- Hover effects: `hover:bg-white/10 hover:scale-105`
- Active states: `bg-white/15 shadow-lg shadow-white/10`

### Customization

1. **Modify colors** in the component files
2. **Adjust animations** by changing motion parameters
3. **Update spacing** by modifying margin and padding classes
4. **Change typography** by updating font classes

## Examples

### Student Dashboard
```bash
/pages/examples/student-dashboard.tsx
```
- View-only interface with course progress
- Assignment tracking and submission status
- Grade overview and calendar integration

### Teacher Dashboard
```bash
/pages/examples/teacher-dashboard.tsx
```
- Class management and student overview
- Assignment creation and grading tools
- Schedule management and communication

### School Admin Dashboard
```bash
/pages/examples/school-admin-dashboard.tsx
```
- System analytics and user management
- Performance monitoring and alerts
- Administrative tools and settings

### Test Page
```bash
/pages/test-dynamic-sidebar.tsx
```
- Interactive demonstration of all features
- Role switching and configuration testing
- Action logging and debugging tools

## Migration Guide

### From Existing Dual Sidebar

1. **Replace imports**:
   ```tsx
   // Old
   import { LayoutWrapper } from '../components/auth/school/dual-sidebar';
   
   // New
   import { DynamicLayoutWrapper } from '../components/auth/dynamic-sidebar';
   ```

2. **Update component usage**:
   ```tsx
   // Old
   <LayoutWrapper showUserNav={true} showHeader={true}>
     {children}
   </LayoutWrapper>
   
   // New
   <DynamicLayoutWrapper userRole="student">
     {children}
   </DynamicLayoutWrapper>
   ```

3. **Add action handling** if needed:
   ```tsx
   <DynamicLayoutWrapper 
     userRole="student"
     onAction={(action, item) => {
       // Handle actions
     }}
   >
     {children}
   </DynamicLayoutWrapper>
   ```

## Best Practices

### Performance
- Use the `DynamicLayoutWrapper` for most use cases
- Implement custom action handlers for complex interactions
- Leverage the built-in state management for consistency

### Accessibility
- All interactive elements include proper ARIA labels
- Keyboard navigation is supported throughout
- Screen reader friendly with semantic HTML

### Responsive Design
- Test on multiple screen sizes
- Use the mobile Sheet components for small screens
- Ensure touch targets are appropriately sized

### State Management
- Rely on the built-in cookie persistence
- Use the provided hooks for state access
- Avoid duplicating state in parent components

## Troubleshooting

### Common Issues

1. **Sidebar not appearing**: Check that the user role is valid and configuration exists
2. **Actions not working**: Ensure action handlers are properly implemented
3. **Styling issues**: Verify Tailwind CSS classes are available
4. **State not persisting**: Check that cookies are enabled

### Debug Mode

Enable debug logging by adding to your component:
```tsx
const handleAction = (action: SidebarAction, item: SidebarItem) => {
  console.log('Debug action:', { action, item });
  // Your action handling
};
```

## Contributing

### Adding New Features

1. **New action types**: Add to the `ActionType` union in types
2. **New sidebar modes**: Extend the `SidebarMode` type
3. **New components**: Follow the existing naming convention
4. **New configurations**: Add to the `roleConfigs` object

### Testing

- Test with all three user roles (student, teacher, school)
- Verify responsive behavior on mobile and desktop
- Check accessibility with screen readers
- Test state persistence across page reloads

## API Reference

### Types

```typescript
type UserRole = 'student' | 'teacher' | 'school';
type SidebarMode = 'dual' | 'left-only' | 'right-only' | 'hidden';
type ActionType = 'flyout' | 'navigate' | 'modal' | 'inline';
```

### Hooks

```typescript
// Main sidebar state hook
const {
  isLeftSidebarOpen,
  isRightSidebarOpen,
  activeFlyout,
  toggleRightSidebar,
  updateActiveFlyout
} = useSidebarState();

// Right flyout specific hook
const {
  isOpen,
  content,
  open,
  close,
  toggle
} = useRightFlyoutState();
```

### Utilities

```typescript
// Get configuration by role
const config = getConfigByRole('student');

// Filter items by role permissions
const filteredItems = filterItemsByRole(items, 'teacher');
```

This dynamic sidebar system provides a powerful, flexible foundation for building role-based interfaces in your Blade application. It eliminates the need for separate layouts while providing rich customization options and excellent user experience across all device types.