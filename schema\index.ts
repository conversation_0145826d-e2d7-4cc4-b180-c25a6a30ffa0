import { blob, boolean, date, link, model, number, string } from 'ronin/schema';

// Core User model - only shared fields
export const User = model({
  slug: 'user',
  pluralSlug: 'users',
  fields: {
    email: string({ required: true, unique: true }),
    emailVerified: boolean({ required: true, defaultValue: false }),
    image: blob(),
    name: string({ required: true }),
    username: string({ unique: true }), // Required by username plugin
    displayUsername: string(), // Non-normalized username
    slug: string({ required: true, unique: true }), // User slug for URLs
    isActive: boolean({ required: true, defaultValue: true }),
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

// Better Auth required models
export const Session = model({
  slug: 'session',
  pluralSlug: 'sessions',
  fields: {
    expiresAt: date({ required: true }),
    ipAddress: string(),
    token: string({ required: true, unique: true }),
    userId: link({ required: true, target: 'user' }),
    userAgent: string(),
    activeOrganizationId: string(), // For organization plugin
  },
});

export const Account = model({
  slug: 'account',
  pluralSlug: 'accounts',
  fields: {
    accessToken: string(),
    accessTokenExpiresAt: date(),
    accountId: string({ required: true }),
    idToken: string(),
    password: string(),
    providerId: string({ required: true }),
    refreshToken: string(),
    refreshTokenExpiresAt: date(),
    scope: string(),
    userId: link({ required: true, target: 'user' }),
  },
});

export const Verification = model({
  slug: 'verification',
  pluralSlug: 'verifications',
  fields: {
    expiresAt: date({ required: true }),
    identifier: string({ required: true }),
    value: string({ required: true }),
  },
});

export const Otp = model({
  slug: 'otp',
  pluralSlug: 'otps',
  fields: {
    identifier: string({ required: true }), // email address
    value: string({ required: true }), // the OTP code
    expiresAt: date({ required: true }),
    type: string({ required: true }), // 'sign-in', 'email-verification', 'forget-password'
    attempts: number({ required: true, defaultValue: 0 }), // Fixed: now a number
    createdAt: date({ required: true }),
  },
});

// Organization models
export const Organization = model({
  slug: 'organization',
  pluralSlug: 'organizations',
  fields: {
    name: string({ required: true }),
    slug: string({ required: true, unique: true }),
    logo: string(),
    metadata: string(), // JSON string for additional data
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

export const Member = model({
  slug: 'member',
  pluralSlug: 'members',
  fields: {
    userId: link({ required: true, target: 'user' }),
    organizationId: link({ required: true, target: 'organization' }),
    role: string({ required: true }), // 'owner', 'admin', 'member'
    createdAt: date({ required: true }),
    // Add composite unique constraint in your database
    // UNIQUE(userId, organizationId)
  },
});

export const Invitation = model({
  slug: 'invitation',
  pluralSlug: 'invitations',
  fields: {
    organizationId: link({ required: true, target: 'organization' }),
    email: string({ required: true }),
    role: string({ required: true }),
    status: string({ required: true, defaultValue: 'pending' }), // 'pending', 'accepted', 'declined', 'expired'
    expiresAt: date({ required: true }),
    inviterId: link({ required: true, target: 'user' }),
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

// School system models
export const School = model({
  slug: 'school',
  pluralSlug: 'schools',
  fields: {
    name: string({ required: true }),
    domain: string({ unique: true }), // Optional: for domain-based signup
    address: string(),
    phone: string(),
    placeId: string(), // Google Places ID
    type: string(), // 'public', 'private', 'charter', etc.
    district: string(),
    studentCount: number(), // Fixed: now a number
    teacherCount: number(), // Fixed: now a number
    isActive: boolean({ required: true, defaultValue: true }),
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

// User role models - normalized approach
export const SchoolAdmin = model({
  slug: 'school_admin',
  pluralSlug: 'school_admins',
  fields: {
    userId: link({ required: true, target: 'user', unique: true }),
    schoolId: link({ required: true, target: 'school' }),
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

export const Teacher = model({
  slug: 'teacher',
  pluralSlug: 'teachers',
  fields: {
    userId: link({ required: true, target: 'user', unique: true }),
    bio: string(),
    isIndependent: boolean({ required: true, defaultValue: false }),
    isVerified: boolean({ required: true, defaultValue: false }),
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

export const Student = model({
  slug: 'student',
  pluralSlug: 'students',
  fields: {
    userId: link({ required: true, target: 'user', unique: true }),
    grade: string(),
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

// Teacher-School relationships
export const TeacherSchool = model({
  slug: 'teacher_school',
  pluralSlug: 'teacher_schools',
  fields: {
    teacherId: link({ required: true, target: 'teacher' }),
    schoolId: link({ required: true, target: 'school' }),
    department: string(),
    status: string({ required: true, defaultValue: 'active' }), // 'active', 'inactive', 'pending'
    invitedBy: link({ target: 'user' }),
    invitedAt: date(),
    joinedAt: date(),
    // Add composite unique constraint: UNIQUE(teacherId, schoolId)
  },
});

// Subject management
export const Subject = model({
  slug: 'subject',
  pluralSlug: 'subjects',
  fields: {
    name: string({ required: true, unique: true }),
    code: string({ unique: true }), // e.g., 'MATH101'
    description: string(),
    isActive: boolean({ required: true, defaultValue: true }),
    createdAt: date({ required: true }),
  },
});

export const TeacherSubject = model({
  slug: 'teacher_subject',
  pluralSlug: 'teacher_subjects',
  fields: {
    teacherId: link({ required: true, target: 'teacher' }),
    subjectId: link({ required: true, target: 'subject' }),
    gradeLevel: string(),
    createdAt: date({ required: true }),
    // Add composite unique constraint: UNIQUE(teacherId, subjectId, gradeLevel)
  },
});

// Class models
export const Class = model({
  slug: 'class',
  pluralSlug: 'classes',
  fields: {
    name: string({ required: true }),
    description: string(),
    teacherId: link({ required: true, target: 'teacher' }),
    schoolId: link({ target: 'school' }), // Optional: for independent teachers
    subjectId: link({ target: 'subject' }),
    gradeLevel: string(),
    maxCapacity: number(), // Fixed: now a number
    currentEnrollment: number({ defaultValue: 0 }), // Track current enrollment
    isActive: boolean({ required: true, defaultValue: true }),
    startDate: date(),
    endDate: date(),
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

// Student-Class enrollment
export const StudentClass = model({
  slug: 'student_class',
  pluralSlug: 'student_classes',
  fields: {
    studentId: link({ required: true, target: 'student' }),
    classId: link({ required: true, target: 'class' }),
    enrolledAt: date({ required: true }),
    status: string({ required: true, defaultValue: 'active' }), // 'active', 'inactive', 'completed', 'dropped'
    finalGrade: string(), // Store final grade when completed
    completedAt: date(),
    // Add composite unique constraint: UNIQUE(studentId, classId)
  },
});

// Student-Teacher relationships (for independent teachers)
export const StudentTeacher = model({
  slug: 'student_teacher',
  pluralSlug: 'student_teachers',
  fields: {
    studentId: link({ required: true, target: 'student' }),
    teacherId: link({ required: true, target: 'teacher' }),
    assignedAt: date({ required: true }),
    status: string({ required: true, defaultValue: 'active' }), // 'active', 'inactive'
    // Add composite unique constraint: UNIQUE(studentId, teacherId)
  },
});

// Waitlist model
export const Waitlist = model({
  slug: 'waitlist',
  pluralSlug: 'waitlists',
  fields: {
    email: string({ required: true, unique: true }),
    name: string({ required: true }),
    userType: string({ required: true }), // 'teacher' | 'school_admin'
    // School-specific fields (only for school_admin type)
    schoolName: string(),
    schoolAddress: string(),
    schoolPlaceId: string(),
    schoolType: string(),
    schoolDistrict: string(),
    estimatedStudentCount: number(),
    estimatedTeacherCount: number(),
    isApproved: boolean({ required: true, defaultValue: false }),
    approvedAt: date(),
    approvedBy: link({ target: 'user' }),
    createdAt: date({ required: true }),
  },
});

// User roles view helper (you might want to create a database view for this)
export const UserRole = model({
  slug: 'user_role',
  pluralSlug: 'user_roles',
  fields: {
    userId: link({ required: true, target: 'user', unique: true }),
    role: string({ required: true }), // 'student', 'teacher', 'school_admin'
    roleId: string({ required: true }), // ID from the specific role table
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

// Indexes you should add in your database:
/*
CREATE INDEX idx_user_email ON users(email);
CREATE INDEX idx_user_username ON users(username);
CREATE INDEX idx_session_token ON sessions(token);
CREATE INDEX idx_session_user ON sessions(user_id);
CREATE INDEX idx_teacher_user ON teachers(user_id);
CREATE INDEX idx_student_user ON students(user_id);
CREATE INDEX idx_class_teacher ON classes(teacher_id);
CREATE INDEX idx_student_class_student ON student_classes(student_id);
CREATE INDEX idx_student_class_class ON student_classes(class_id);
CREATE INDEX idx_teacher_school_teacher ON teacher_schools(teacher_id);
CREATE INDEX idx_teacher_school_school ON teacher_schools(school_id);

-- Composite unique constraints:
ALTER TABLE members ADD CONSTRAINT unique_user_org UNIQUE (user_id, organization_id);
ALTER TABLE teacher_schools ADD CONSTRAINT unique_teacher_school UNIQUE (teacher_id, school_id);
ALTER TABLE teacher_subjects ADD CONSTRAINT unique_teacher_subject_grade UNIQUE (teacher_id, subject_id, grade_level);
ALTER TABLE student_classes ADD CONSTRAINT unique_student_class UNIQUE (student_id, class_id);
ALTER TABLE student_teachers ADD CONSTRAINT unique_student_teacher UNIQUE (student_id, teacher_id);
*/