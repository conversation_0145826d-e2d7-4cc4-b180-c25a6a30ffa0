import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface WaitlistEmailProps {
  name: string;
}

export const WaitlistEmail: React.FC<Readonly<WaitlistEmailProps>> = ({
  name,
}) => (
  <Html>
    <Head />
    <Preview>Welcome to the Penned Teacher Waitlist - AI Detection Made Simple</Preview>
    <Body style={main as any}>
      <Container style={container as any}>
        <Heading style={h1 as any}>Welcome, Educator! 👩‍🏫</Heading>
        <Text style={text as any}>
          Hi {name},
        </Text>
        <Text style={text as any}>
          Thank you for joining the Penned Teacher waitlist! We're building powerful AI detection tools
          specifically designed for educators who want to maintain academic integrity while nurturing
          authentic learning.
        </Text>
        <Text style={text as any}>
          As a teacher on our waitlist, you'll be among the first to:
        </Text>
        <Text style={text as any}>
          • Get early access to instant AI detection tools<br/>
          • Receive resources for fostering authentic student writing<br/>
          • Join our educator community dedicated to academic integrity
        </Text>
        <Text style={text as any}>
          We'll keep you updated on our progress and notify you as soon as we're ready to launch.
          Your feedback will be invaluable in shaping Penned for educators.
        </Text>
        <Text style={textSmall as any}>
          Best regards,<br/>
          The Penned Team
        </Text>
      </Container>
    </Body>
  </Html>
);

export default WaitlistEmail;

const main = {
  backgroundColor: '#000000',
  margin: '0 auto',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
};

const container = {
  margin: 'auto',
  padding: '96px 20px 64px',
};

const h1 = {
  color: '#ffffff',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '40px',
  margin: '0 0 20px',
};

const text = {
  color: '#aaaaaa',
  fontSize: '14px',
  lineHeight: '24px',
  margin: '0 0 20px',
};

const textSmall = {
  color: '#888888',
  fontSize: '12px',
  lineHeight: '20px',
  margin: '20px 0 0',
};
