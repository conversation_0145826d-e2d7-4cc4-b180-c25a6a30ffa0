import {
  Home,
  BookOpen,
  Users,
  Calendar,
  MessageSquare,
  Settings,
  Bell,
  User,
  GraduationCap,
  School,
  FileText,
  BarChart3,
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  Download,
  Upload,
  Share,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  HelpCircle,
  Zap,
  Target,
  Award,
  TrendingUp,
  Database,
  Shield,
  Globe,
  Mail,
  Phone,
  MapPin,
  Camera,
  Video,
  Mic,
  Headphones,
  Monitor,
  Smartphone,
  Tablet,
  Laptop,
  Server,
  Cloud,
  Wifi,
  Battery,
  Power,
  Refresh,
  Save,
  Copy,
  Cut,
  Paste,
  Undo,
  Redo,
  ZoomIn,
  ZoomOut,
  Maximize,
  Minimize,
  MoreHorizontal,
  MoreVertical,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  ExternalLink,
  Link,
  Unlink,
  Lock,
  Unlock,
  Key,
  UserCheck,
  UserX,
  UserPlus,
  UserMinus,
  Users2,
  Team,
  Crown,
  Badge,
  Flag,
  Tag,
  Bookmark,
  Heart,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  Send,
  Reply,
  Forward,
  Archive,
  Inbox,
  Outbox,
  Drafts,
  Spam,
  Trash,
  Folder,
  FolderOpen,
  FolderPlus,
  File,
  FilePlus,
  FileEdit,
  FileX,
  FileCheck,
  Image,
  Music,
  PlayCircle,
  PauseCircle,
  StopCircle,
  SkipBack,
  SkipForward,
  Volume,
  VolumeX,
  Volume1,
  Volume2,
  Shuffle,
  Repeat,
  Repeat1,
  List,
  Grid,
  Columns,
  Rows,
  Layout,
  Sidebar,
  PanelLeft,
  PanelRight,
  PanelTop,
  PanelBottom,
  Split,
  Combine,
  Expand,
  Shrink,
  Move,
  Resize,
  Rotate,
  Flip,
  Crop,
  Scissors,
  Paintbrush,
  Palette,
  Pipette,
  Ruler,
  Compass,
  Triangle,
  Square,
  Circle,
  Pentagon,
  Hexagon,
  Octagon,
  Diamond,
  Heart as HeartShape,
  Star as StarShape,
  Sun,
  Moon,
  CloudRain,
  CloudSnow,
  CloudLightning,
  Wind,
  Thermometer,
  Droplets,
  Flame,
  Snowflake,
  Umbrella,
  Rainbow,
  Sunrise,
  Sunset,
  Mountain,
  Tree,
  Flower,
  Leaf,
  Seedling,
  Cactus,
  PalmTree,
  Evergreen,
  Deciduous,
  Grass,
  Clover,
  Mushroom,
  Shell,
  Waves,
  Fish,
  Bird,
  Butterfly,
  Bug,
  Ant,
  Bee,
  Spider,
  Snail,
  Turtle,
  Rabbit,
  Cat,
  Dog,
  Horse,
  Cow,
  Pig,
  Sheep,
  Goat,
  Chicken,
  Duck,
  Turkey,
  Eagle,
  Owl,
  Penguin,
  Dolphin,
  Whale,
  Shark,
  Octopus,
  Crab,
  Lobster,
  Shrimp,
  Jellyfish,
  Starfish,
  Seahorse,
  Coral,
  Seaweed,
  Anchor,
  Ship,
  Boat,
  Sailboat,
  Submarine,
  Plane,
  Helicopter,
  Rocket,
  Satellite,
  Car,
  Truck,
  Bus,
  Train,
  Bicycle,
  Motorcycle,
  Scooter,
  Skateboard,
  RollerSkate,
  Sled,
  Skis,
  Snowboard,
  Surfboard,
  Kayak,
  Canoe,
  Raft,
  Parachute,
  Balloon,
  Kite,
  Tent,
  Backpack,
  Compass as CompassIcon,
  Map,
  Binoculars,
  Flashlight,
  Lantern,
  Candle,
  Matches,
  Lighter,
  Cigarette,
  Pipe,
  Cigar,
  Hookah,
  Bong,
  Pill,
  Syringe,
  Bandage,
  Thermometer as ThermometerIcon,
  Stethoscope,
  Microscope,
  Telescope,
  Magnet,
  Atom,
  Molecule,
  DNA,
  Virus,
  Bacteria,
  Cell,
  Bone,
  Skull,
  Brain,
  Lungs,
  Liver,
  Kidney,
  Stomach,
  Intestines,
  Bladder,
  Uterus,
  Ovary,
  Testis,
  Sperm,
  Egg,
  Embryo,
  Baby,
  Child,
  Adult,
  Elderly,
  Male,
  Female,
  Transgender,
  Intersex,
  Nonbinary,
  Genderfluid,
  Agender,
  Bigender,
  Demigender,
  Pangender,
  Genderqueer,
  TwoSpirit,
  ThirdGender,
  Neutrois,
  Maverique,
  Aporagender,
  Libragender,
  Demiboy,
  Demigirl,
  Boyflux,
  Girlflux,
  Genderflux,
  Genderfae,
  Genderfaun,
  Genderselkie,
  Gendervoid,
  Genderwitched,
  Xenogender,
  Aesthetigender,
  Musicagender,
  Videogamegender,
  Catgender,
  Doggender,
  Wolfgender,
  Foxgender,
  Beargender,
  Liongender,
  Tigergender,
  Leopardgender,
  Cheetahgender,
  Jaguargender,
  Panthergender,
  Lynxgender,
  Bobcatgender,
  Ocelotgender,
  Serval,
  Caracal,
  Margay,
  Jaguarundi,
  Kodkod,
  Oncilla,
  Pallas,
  Manul,
  Sandcat,
  Blackfooted,
  Rustyspotted,
  Flatheaded,
  Fishing,
  Leopardcat,
  Asianpalm,
  Pampas,
  Andean,
  Geoffroys,
  Guignas,
  Margaycat,
  Ocelotcat,
  Tigercat,
  Wildcat,
  Jungle,
  Sandcatgender,
  Desertsand,
  Beachsand,
  Whitesand,
  Blacksand,
  Redsand,
  Yellowsand,
  Brownsand,
  Graysand,
  Pinksand,
  Purplesand,
  Bluesand,
  Greensand,
  Orangesand,
  Goldsand,
  Silversand,
  Coppersand,
  Bronzesand,
  Ironsand,
  Steelsand,
  Aluminumsand,
  Titaniumsand,
  Platinumsand,
  Palladiumsand,
  Rhodiumsand,
  Iridiumsand,
  Osmiumsand,
  Rutheniumsand,
  Rheniumsand,
  Tungstenssand,
  Tantalumsand,
  Hafniumsand,
  Zirconiumsand,
  Niobiumsand,
  Molybdenumsand,
  Technetiumsand,
  Rutheiumsand,
  Rhodiumsand as RhodiumsandIcon,
  Palladiumsand as PalladiumsandIcon,
  Silversand as SilversandIcon,
  Cadmiumsand,
  Indiumsand,
  Tinsand,
  Antimonysand,
  Telluriumsand,
  Iodinesand,
  Xenonsand,
  Cesiumsand,
  Bariumsand,
  Lanthanumsand,
  Ceriumsand,
  Praseodymiumsand,
  Neodymiumsand,
  Promethiumsand,
  Samariumsand,
  Europiumsand,
  Gadoliniumsand,
  Terbiumsand,
  Dysprosiumsand,
  Holmiumsand,
  Erbiumsand,
  Thuliumsand,
  Ytterbiumsand,
  Lutetiumsand,
  Actiniumsand,
  Thoriumsand,
  Protactiniumsand,
  Uraniumsand,
  Neptuniumsand,
  Plutoniumsand,
  Americiumsand,
  Curiumsand,
  Berkeliumsand,
  Californiumsand,
  Einsteinium,
  Fermiumsand,
  Mendeleviumsand,
  Nobeliumsand,
  Lawrenciumsand,
  Rutherfordiumsand,
  Dubniumsand,
  Seaborgiumsand,
  Bohriumsand,
  Hassiumsand,
  Meitneriumsand,
  Darmstadtiumsand,
  Roentgeniumsand,
  Coperniciumsand,
  Nihoniumsand,
  Fleroviumsand,
  Moscoviumsand,
  Livermoriumsand,
  Tennesinesand,
  Oganessonsand
} from 'lucide-react';
import { SidebarConfig, RoleConfigs } from '../types/sidebar-config';

// Student Configuration
const studentConfig: SidebarConfig = {
  mode: 'dual',
  leftSidebar: {
    width: 'icon',
    allowFlyouts: true,
    sections: [
      {
        id: 'main',
        items: [
          {
            id: 'dashboard',
            label: 'Dashboard',
            icon: Home,
            action: { type: 'navigate', target: '/student/dashboard' }
          },
          {
            id: 'courses',
            label: 'My Courses',
            icon: BookOpen,
            action: { type: 'flyout', target: 'courses' },
            badge: 3
          },
          {
            id: 'assignments',
            label: 'Assignments',
            icon: FileText,
            action: { type: 'flyout', target: 'assignments' },
            badge: 5
          },
          {
            id: 'grades',
            label: 'Grades',
            icon: BarChart3,
            action: { type: 'navigate', target: '/student/grades' }
          },
          {
            id: 'calendar',
            label: 'Calendar',
            icon: Calendar,
            action: { type: 'flyout', target: 'calendar' }
          }
        ]
      },
      {
        id: 'communication',
        title: 'Communication',
        items: [
          {
            id: 'messages',
            label: 'Messages',
            icon: MessageSquare,
            action: { type: 'flyout', target: 'messages' },
            badge: 2
          },
          {
            id: 'announcements',
            label: 'Announcements',
            icon: Bell,
            action: { type: 'flyout', target: 'announcements' }
          }
        ]
      }
    ]
  },
  rightSidebar: {
    allowFlyouts: true,
    defaultContent: 'quick-actions',
    sections: [
      {
        id: 'quick-actions',
        title: 'Quick Actions',
        items: [
          {
            id: 'submit-assignment',
            label: 'Submit Assignment',
            icon: Upload,
            action: { type: 'modal', target: 'submit-assignment' }
          },
          {
            id: 'join-class',
            label: 'Join Class',
            icon: Users,
            action: { type: 'modal', target: 'join-class' }
          }
        ]
      }
    ]
  },
  userNav: {
    show: true,
    position: 'top-right',
    items: [
      {
        id: 'profile',
        label: 'Profile',
        icon: User,
        action: { type: 'flyout', target: 'user-info' }
      },
      {
        id: 'settings',
        label: 'Settings',
        icon: Settings,
        action: { type: 'flyout', target: 'settings' }
      },
      {
        id: 'notifications',
        label: 'Notifications',
        icon: Bell,
        action: { type: 'flyout', target: 'notifications' },
        badge: 3
      }
    ]
  },
  header: {
    show: true,
    title: 'Student Portal'
  },
  permissions: {
    canView: true,
    canCreate: false,
    canEdit: false,
    canDelete: false
  }
};

// Teacher Configuration
const teacherConfig: SidebarConfig = {
  mode: 'dual',
  leftSidebar: {
    width: 'icon',
    allowFlyouts: true,
    sections: [
      {
        id: 'main',
        items: [
          {
            id: 'dashboard',
            label: 'Dashboard',
            icon: Home,
            action: { type: 'navigate', target: '/teacher/dashboard' }
          },
          {
            id: 'classes',
            label: 'My Classes',
            icon: GraduationCap,
            action: { type: 'flyout', target: 'classes' },
            badge: 4
          },
          {
            id: 'students',
            label: 'Students',
            icon: Users,
            action: { type: 'flyout', target: 'students' }
          },
          {
            id: 'assignments',
            label: 'Assignments',
            icon: FileText,
            action: { type: 'flyout', target: 'assignments' }
          },
          {
            id: 'gradebook',
            label: 'Gradebook',
            icon: BarChart3,
            action: { type: 'navigate', target: '/teacher/gradebook' }
          },
          {
            id: 'calendar',
            label: 'Calendar',
            icon: Calendar,
            action: { type: 'flyout', target: 'calendar' }
          }
        ]
      },
      {
        id: 'tools',
        title: 'Teaching Tools',
        items: [
          {
            id: 'create-assignment',
            label: 'Create Assignment',
            icon: Plus,
            action: { type: 'modal', target: 'create-assignment' }
          },
          {
            id: 'create-quiz',
            label: 'Create Quiz',
            icon: HelpCircle,
            action: { type: 'modal', target: 'create-quiz' }
          },
          {
            id: 'attendance',
            label: 'Attendance',
            icon: CheckCircle,
            action: { type: 'flyout', target: 'attendance' }
          }
        ]
      },
      {
        id: 'communication',
        title: 'Communication',
        items: [
          {
            id: 'messages',
            label: 'Messages',
            icon: MessageSquare,
            action: { type: 'flyout', target: 'messages' },
            badge: 5
          },
          {
            id: 'announcements',
            label: 'Announcements',
            icon: Bell,
            action: { type: 'flyout', target: 'announcements' }
          }
        ]
      }
    ]
  },
  rightSidebar: {
    allowFlyouts: true,
    defaultContent: 'class-overview',
    sections: [
      {
        id: 'class-overview',
        title: 'Class Overview',
        items: [
          {
            id: 'recent-submissions',
            label: 'Recent Submissions',
            icon: Clock,
            action: { type: 'inline' }
          },
          {
            id: 'pending-grades',
            label: 'Pending Grades',
            icon: AlertCircle,
            action: { type: 'inline' },
            badge: 12
          }
        ]
      },
      {
        id: 'quick-actions',
        title: 'Quick Actions',
        items: [
          {
            id: 'grade-assignments',
            label: 'Grade Assignments',
            icon: Edit,
            action: { type: 'modal', target: 'grade-assignments' }
          },
          {
            id: 'send-message',
            label: 'Send Message',
            icon: Send,
            action: { type: 'modal', target: 'send-message' }
          }
        ]
      }
    ]
  },
  userNav: {
    show: true,
    position: 'top-right',
    items: [
      {
        id: 'profile',
        label: 'Profile',
        icon: User,
        action: { type: 'flyout', target: 'user-info' }
      },
      {
        id: 'settings',
        label: 'Settings',
        icon: Settings,
        action: { type: 'flyout', target: 'settings' }
      },
      {
        id: 'notifications',
        label: 'Notifications',
        icon: Bell,
        action: { type: 'flyout', target: 'notifications' },
        badge: 7
      }
    ]
  },
  header: {
    show: true,
    title: 'Teacher Portal',
    actions: [
      {
        id: 'create-content',
        label: 'Create Content',
        icon: Plus,
        action: { type: 'modal', target: 'create-content' }
      }
    ]
  },
  permissions: {
    canView: true,
    canCreate: true,
    canEdit: true,
    canDelete: true
  }
};

// School Admin Configuration
const schoolConfig: SidebarConfig = {
  mode: 'dual',
  leftSidebar: {
    width: 'icon',
    allowFlyouts: true,
    sections: [
      {
        id: 'main',
        items: [
          {
            id: 'dashboard',
            label: 'Dashboard',
            icon: Home,
            action: { type: 'navigate', target: '/school/dashboard' }
          },
          {
            id: 'analytics',
            label: 'Analytics',
            icon: TrendingUp,
            action: { type: 'flyout', target: 'analytics' }
          },
          {
            id: 'users',
            label: 'User Management',
            icon: Users,
            action: { type: 'flyout', target: 'users' }
          },
          {
            id: 'classes',
            label: 'Class Management',
            icon: School,
            action: { type: 'flyout', target: 'classes' }
          },
          {
            id: 'reports',
            label: 'Reports',
            icon: FileText,
            action: { type: 'flyout', target: 'reports' }
          }
        ]
      },
      {
        id: 'administration',
        title: 'Administration',
        items: [
          {
            id: 'settings',
            label: 'School Settings',
            icon: Settings,
            action: { type: 'flyout', target: 'school-settings' }
          },
          {
            id: 'security',
            label: 'Security',
            icon: Shield,
            action: { type: 'flyout', target: 'security' }
          },
          {
            id: 'integrations',
            label: 'Integrations',
            icon: Zap,
            action: { type: 'flyout', target: 'integrations' }
          }
        ]
      },
      {
        id: 'communication',
        title: 'Communication',
        items: [
          {
            id: 'announcements',
            label: 'Announcements',
            icon: Bell,
            action: { type: 'flyout', target: 'announcements' }
          },
          {
            id: 'messages',
            label: 'Messages',
            icon: MessageSquare,
            action: { type: 'flyout', target: 'messages' }
          }
        ]
      }
    ]
  },
  rightSidebar: {
    allowFlyouts: true,
    defaultContent: 'system-overview',
    sections: [
      {
        id: 'system-overview',
        title: 'System Overview',
        items: [
          {
            id: 'active-users',
            label: 'Active Users',
            icon: Users,
            action: { type: 'inline' }
          },
          {
            id: 'system-health',
            label: 'System Health',
            icon: Activity,
            action: { type: 'inline' }
          },
          {
            id: 'recent-activity',
            label: 'Recent Activity',
            icon: Clock,
            action: { type: 'inline' }
          }
        ]
      },
      {
        id: 'quick-actions',
        title: 'Quick Actions',
        items: [
          {
            id: 'add-user',
            label: 'Add User',
            icon: UserPlus,
            action: { type: 'modal', target: 'add-user' }
          },
          {
            id: 'create-class',
            label: 'Create Class',
            icon: Plus,
            action: { type: 'modal', target: 'create-class' }
          },
          {
            id: 'send-announcement',
            label: 'Send Announcement',
            icon: Send,
            action: { type: 'modal', target: 'send-announcement' }
          }
        ]
      }
    ]
  },
  userNav: {
    show: true,
    position: 'top-right',
    items: [
      {
        id: 'profile',
        label: 'Profile',
        icon: User,
        action: { type: 'flyout', target: 'user-info' }
      },
      {
        id: 'admin-settings',
        label: 'Admin Settings',
        icon: Settings,
        action: { type: 'flyout', target: 'admin-settings' }
      },
      {
        id: 'notifications',
        label: 'Notifications',
        icon: Bell,
        action: { type: 'flyout', target: 'notifications' },
        badge: 15
      }
    ]
  },
  header: {
    show: true,
    title: 'School Administration',
    actions: [
      {
        id: 'system-settings',
        label: 'System Settings',
        icon: Settings,
        action: { type: 'modal', target: 'system-settings' }
      },
      {
        id: 'backup',
        label: 'Backup System',
        icon: Database,
        action: { type: 'modal', target: 'backup-system' }
      }
    ]
  },
  permissions: {
    canView: true,
    canCreate: true,
    canEdit: true,
    canDelete: true
  }
};

export const roleConfigs: RoleConfigs = {
  student: studentConfig,
  teacher: teacherConfig,
  school: schoolConfig
};

// Helper function to get config by role
export function getConfigByRole(role: UserRole): SidebarConfig {
  return roleConfigs[role];
}

// Helper function to filter items by permissions
export function filterItemsByRole(items: any[], userRole: UserRole): any[] {
  return items.filter(item => {
    if (!item.permissions) return true;
    return item.permissions.includes(userRole);
  });
}