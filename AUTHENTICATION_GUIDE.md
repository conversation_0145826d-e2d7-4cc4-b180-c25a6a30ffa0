# Authentication & Access Control Guide

## Overview

Your application uses a comprehensive multi-role authentication system with Better Auth, providing secure access control for students, teachers, and school administrators.

## Architecture

### 1. **No @auth.ts** ❌
The old `@auth.ts` file is **NOT** being used. You've migrated to a more sophisticated system.

### 2. **Better Auth Multi-Instance Setup** ✅
- `lib/student/auth.ts` - Student authentication server
- `lib/teacher/auth.ts` - Teacher authentication server  
- `lib/school/auth.ts` - School admin authentication server
- `lib/auth-client.ts` - Unified client that manages all three

### 3. **Unified Session Management** ✅
- `useUnifiedSession()` - Checks all auth instances and returns active session
- `useAuth()` - Enhanced hook with helper methods and route checking
- `useRouteProtection()` - Global route protection system

## How Access Control Works

### Global Route Protection

The system now has **comprehensive access control** that prevents:

1. **Unauthenticated users** from accessing protected content
2. **Authenticated users** from accessing public pages (homepage, login)
3. **Users with wrong roles** from accessing other role areas
4. **Cross-role access** - students can't access teacher/school areas

### Route Configuration

```typescript
// utils/route-protection.ts
export const routeConfig: RouteConfig[] = [
  // Public routes (only accessible when NOT authenticated)
  { path: '/', type: 'public' },
  { path: '/login', type: 'public' },
  
  // Protected routes with role restrictions
  { path: '/student', type: 'protected', allowedRoles: ['student'] },
  { path: '/teacher', type: 'protected', allowedRoles: ['teacher'] },
  { path: '/school', type: 'protected', allowedRoles: ['school_admin'] },
];
```

### Protection Layers

#### Layer 1: Global Route Protection (Root Layout)
```typescript
// pages/layout.tsx
const { user, loading } = useRouteProtection();
```
- Runs on every page load
- Redirects authenticated users away from public pages
- Redirects unauthenticated users to login
- Enforces role-based access

#### Layer 2: Role-Specific Layout Protection
```typescript
// pages/student/layout.tsx, pages/teacher/layout.tsx, pages/school/layout.tsx
const { user, loading } = useAuth();

if (!user) {
  redirect('/login?role=student');
  return null;
}

if (user.role !== 'student') {
  redirect(user.getCorrectDashboardUrl());
  return null;
}
```

#### Layer 3: Component-Level Protection
```typescript
// Use withRouteProtection wrapper
const ProtectedComponent = withRouteProtection(MyComponent, ['teacher', 'school_admin']);

// Or use hooks in components
const { canAccessRoles } = useAuth();
if (!canAccessRoles(['teacher'])) {
  return <AccessDenied />;
}
```

## User Flow Examples

### 1. **Unauthenticated User**
- Visits `/` → Can access homepage
- Visits `/student/dashboard` → Redirected to `/login?role=student`
- Visits `/teacher/classes` → Redirected to `/login?role=teacher`

### 2. **Authenticated Student**
- Visits `/` → Redirected to `/student/{slug}`
- Visits `/login` → Redirected to `/student/{slug}`
- Visits `/student/dashboard` → ✅ Access granted
- Visits `/teacher/classes` → Redirected to `/student/{slug}`

### 3. **Authenticated Teacher**
- Visits `/` → Redirected to `/teacher/{slug}`
- Visits `/student/dashboard` → Redirected to `/teacher/{slug}`
- Visits `/teacher/classes` → ✅ Access granted
- Visits `/school/admin` → Redirected to `/teacher/{slug}`

### 4. **Authenticated School Admin**
- Visits `/` → Redirected to `/school/{slug}`
- Visits `/student/dashboard` → Redirected to `/school/{slug}`
- Visits `/teacher/classes` → Redirected to `/school/{slug}`
- Visits `/school/admin` → ✅ Access granted

## Key Features

### ✅ **Complete Access Control**
- No unauthorized access to any content
- Authenticated users cannot access public pages
- Role-based restrictions enforced globally

### ✅ **Automatic Redirects**
- Smart redirects based on user role and current location
- Maintains user experience with appropriate role hints

### ✅ **Instant Rendering (No Loading States)**
- Following Blade's philosophy: instant redirects, no loading spinners
- Prevents flash of unauthorized content through immediate redirects

### ✅ **Enhanced useAuth Hook**
```typescript
const { 
  user,                    // Enhanced user object with helper methods
  loading,                 // Loading state
  signOut,                 // Sign out function
  isAuthenticated,         // Boolean authentication status
  canAccessRoute,          // Check access to specific route
  canAccessRoles          // Check access for multiple roles
} = useAuth();

// User object includes:
user.isOnCorrectRoute()           // Check if on correct role route
user.getCorrectDashboardUrl()     // Get correct dashboard URL
```

### ✅ **Flexible Protection**
```typescript
// Protect entire components
const ProtectedPage = withRouteProtection(MyPage, ['teacher', 'school_admin']);

// Check access in components
const { canAccessRoles } = useAuth();
if (canAccessRoles(['teacher', 'school_admin'])) {
  // Render admin content
}
```

## Security Benefits

1. **No Public Access for Authenticated Users** - Prevents confusion and maintains clear user flows
2. **Role Isolation** - Complete separation between student, teacher, and school admin areas
3. **Automatic Session Management** - Unified session handling across all auth instances
4. **CSRF Protection** - Built into Better Auth
5. **Secure Cookies** - HTTP-only session cookies
6. **Loading State Protection** - Prevents unauthorized content flash

## Migration from @auth.ts

The old `@auth.ts` system has been completely replaced with:

| Old System | New System |
|------------|------------|
| Single auth instance | Multiple role-specific auth instances |
| Manual route protection | Automatic global route protection |
| Basic session checking | Enhanced session management with helpers |
| Limited role support | Full role-based access control |
| Manual redirects | Smart automatic redirects |

## Best Practices

1. **Always use `useAuth()`** for authentication state
2. **Follow Blade's philosophy** - no loading states, instant redirects
3. **Use role-specific redirects** for better UX
4. **Leverage helper methods** like `canAccessRoles()`
5. **Use `ProtectedContent` component** for conditional rendering
6. **Test all role combinations** to ensure proper access control

## Testing Access Control

To verify the system works correctly:

1. **Test unauthenticated access** - Try accessing protected routes
2. **Test cross-role access** - Login as student, try accessing teacher routes
3. **Test public page access** - Login and try accessing homepage
4. **Test redirects** - Verify users land on correct dashboards
5. **Test loading states** - Ensure no unauthorized content flashes

Your authentication system now provides **enterprise-grade access control** with complete role isolation and automatic protection across all routes.