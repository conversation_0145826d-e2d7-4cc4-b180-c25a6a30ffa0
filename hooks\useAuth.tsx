// hooks/useAuth.tsx
'use client';

import { useUnifiedSession } from '../lib/auth-client';
import { useLocation } from 'blade/hooks';
import type { UseAuthReturn, ExtendedUser, UserRole } from '../lib/types';

export const useAuth = (): UseAuthReturn => {
  const { session, loading, signOut, role } = useUnifiedSession();
  const location = useLocation();
  
  // Enhanced user object with additional properties
  const user: ExtendedUser | null = session?.user ? {
    ...session.user,
    // Add utility methods to the user object
    isOnCorrectRoute: () => {
      if (!session.user.role) return false;
      const rolePrefix = session.user.role === 'school_admin' ? 'school' : session.user.role;
      return location.pathname.startsWith(`/${rolePrefix}`);
    },
    getCorrectDashboardUrl: () => {
      if (!session.user.role) return '/login';
      const rolePrefix = session.user.role === 'school_admin' ? 'school' : session.user.role;
      return `/${rolePrefix}/${session.user.slug}`;
    }
  } as ExtendedUser & {
    isOnCorrectRoute: () => boolean;
    getCorrectDashboardUrl: () => string;
  } : null;
  
  return {
    user,
    session: session?.session || null,
    // Note: loading is intentionally not exposed to discourage loading states
    // Internal loading state is still used by route protection for immediate redirects
    signOut,
    role,
    isAuthenticated: !!session?.user,
    
    // Helper methods
    canAccessRoute: (requiredRole?: string) => {
      if (!user) return false;
      if (!requiredRole) return true;
      return user.role === requiredRole;
    },
    
    canAccessRoles: (allowedRoles: string[]) => {
      if (!user) return false;
      return allowedRoles.includes(user.role);
    },
    
    // Internal method for route protection - not intended for general use
    _getLoadingState: () => loading
  };
};

export default useAuth;