// components/LogoutButton.client.tsx
import { useAuth } from '../hooks/useAuth';

const LogoutButton = () => {
  const { signOut, user } = useAuth();
  
  const handleLogout = async () => {
    try {
      await signOut();
      // Redirect to home page
      window.location.href = '/';
    } catch (error) {
      console.error('Logout failed:', error);
      // Force redirect even if logout fails
      window.location.href = '/';
    }
  };
  
  return (
    <button 
      onClick={handleLogout} 
      className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-md text-sm transition-colors"
    >
      Logout
    </button>
  );
};

export default LogoutButton;