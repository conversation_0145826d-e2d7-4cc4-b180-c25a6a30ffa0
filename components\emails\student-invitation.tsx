import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Text,
  Button,
  Section,
} from '@react-email/components';
import * as React from 'react';

interface StudentInvitationEmailProps {
  studentName: string;
  teacherName: string;
  username: string;
  email: string;
  classes: string[];
  loginUrl?: string;
  password?: string;
}

export const StudentInvitationEmail: React.FC<Readonly<StudentInvitationEmailProps>> = ({
  studentName,
  teacherName,
  username,
  email,
  classes,
  loginUrl = 'https://penned.studio/login?role=student',
  password = 'student123',
}) => (
  <Html>
    <Head />
    <Preview>Welcome to Penned - Your Student Account is Ready!</Preview>
    <Body style={main as any}>
      <Container style={container as any}>
        <Heading style={h1 as any}>Welcome to Penned, {studentName}! 🎓</Heading>
        <Text style={text as any}>
          Hi {studentName},
        </Text>
        <Text style={text as any}>
          Your teacher, {teacher<PERSON><PERSON>}, has created a student account for you on Penned.
          You can now access our platform to submit assignments and track your academic progress.
        </Text>
        
        <Section style={credentialsSection as any}>
          <Heading style={h2 as any}>Your Login Credentials</Heading>
          <Text style={credentialText as any}>
            <strong>Username:</strong> {username}<br/>
            <strong>Email:</strong> {email}<br/>
            <strong>Password:</strong> {password}
          </Text>
          <Text style={warningText as any}>
            ⚠️ <strong>Important:</strong> Please change your password after your first login for security.
          </Text>
          <Text style={text as any}>
            You can sign in using either your username or email address.
          </Text>
        </Section>

        {classes.length > 0 && (
          <Section style={classesSection as any}>
            <Heading style={h2 as any}>Your Classes</Heading>
            <Text style={text as any}>
              You've been enrolled in the following classes:
            </Text>
            <Text style={classList as any}>
              {classes.map((className, index) => (
                <span key={className}>
                  • {className}
                  {index < classes.length - 1 && <br />}
                </span>
              ))}
            </Text>
          </Section>
        )}

        <Section style={buttonSection as any}>
          <Button style={button as any} href={loginUrl}>
            Sign In to Penned
          </Button>
        </Section>

        <Text style={text as any}>
          If you have any questions about using Penned or need help with your account,
          please reach out to your teacher or contact our support team.
        </Text>

        <Text style={textSmall as any}>
          Best regards,<br/>
          The Penned Team
        </Text>
      </Container>
    </Body>
  </Html>
);

export default StudentInvitationEmail;

const main = {
  backgroundColor: '#000000',
  margin: '0 auto',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
};

const container = {
  margin: 'auto',
  padding: '96px 20px 64px',
};

const h1 = {
  color: '#ffffff',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '40px',
  margin: '0 0 20px',
};

const h2 = {
  color: '#ffffff',
  fontSize: '18px',
  fontWeight: '600',
  lineHeight: '28px',
  margin: '0 0 12px',
};

const text = {
  color: '#aaaaaa',
  fontSize: '14px',
  lineHeight: '24px',
  margin: '0 0 20px',
};

const textSmall = {
  color: '#888888',
  fontSize: '12px',
  lineHeight: '20px',
  margin: '20px 0 0',
};

const credentialsSection = {
  margin: '32px 0',
  padding: '24px',
  backgroundColor: '#1a1a1a',
  borderRadius: '8px',
  border: '1px solid #333333',
};

const credentialText = {
  color: '#ffffff',
  fontSize: '14px',
  lineHeight: '24px',
  margin: '0 0 16px',
  fontFamily: 'monospace',
};

const warningText = {
  color: '#fbbf24',
  fontSize: '13px',
  lineHeight: '20px',
  margin: '16px 0',
  padding: '12px',
  backgroundColor: '#451a03',
  borderRadius: '6px',
  border: '1px solid #92400e',
};

const classesSection = {
  margin: '32px 0',
  padding: '24px',
  backgroundColor: '#0a1a0a',
  borderRadius: '8px',
  border: '1px solid #2a4a2a',
};

const classList = {
  color: '#ffffff',
  fontSize: '14px',
  lineHeight: '24px',
  margin: '0 0 16px',
};

const buttonSection = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#3b82f6',
  borderRadius: '8px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  margin: '0 auto',
};
