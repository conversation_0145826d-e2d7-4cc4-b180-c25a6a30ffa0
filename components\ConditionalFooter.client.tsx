// components/ConditionalFooter.client.tsx
'use client';

import { useAuth } from '../hooks/useAuth';
import { useLocation } from 'blade/hooks';
import { Footer } from './footer';
import type { Theme } from './theme-toggle.client';

interface ConditionalFooterProps {
  theme: Theme | null;
}

export const ConditionalFooter: React.FC<ConditionalFooterProps> = ({ theme }) => {
  const { user, _getLoadingState } = useAuth();
  const loading = _getLoadingState();
  const location = useLocation();

  // Don't render footer if still loading auth state
  if (loading) {
    return null;
  }

  // Don't render footer for authenticated users on public pages (they'll be redirected)
  if (user && (location.pathname === '/' || location.pathname === '/login')) {
    return null;
  }

  // Render footer for all other cases
  return <Footer theme={theme} />;
};
