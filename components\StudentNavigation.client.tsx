
// components/student/StudentNavigation.client.tsx
'use client';

import { useAuth } from '../hooks/useAuth';
import LogoutButton from './LogoutButton.client';

const StudentNavigation = () => {
  const { user } = useAuth();
  
  // This component assumes user is authenticated and is a student
  // because it's wrapped by StudentAuthWrapper
  if (!user) return null;
  
  return (
    <header className="bg-blue-600 text-white shadow-lg">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center space-x-8">
            <h1 className="text-xl font-bold">Student Portal</h1>
            <div className="hidden md:flex space-x-6">
              <a href={`/student/${user.slug}`} className="hover:text-blue-200">
                Home
              </a>
              <a href="/student/courses" className="hover:text-blue-200">
                My Courses
              </a>
              <a href="/student/assignments" className="hover:text-blue-200">
                Assignments
              </a>
              <a href="/student/grades" className="hover:text-blue-200">
                Grades
              </a>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-blue-100">Welcome, {user.name}</span>
            <LogoutButton />
          </div>
        </div>
      </nav>
    </header>
  );
};

export default StudentNavigation;