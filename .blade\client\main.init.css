/* node_modules/@ncdai/react-wheel-picker/dist/index.css */
[data-rwp-wrapper] {
  position: relative;
  overflow: hidden;
  display: flex;
  width: 100%;
  align-items: stretch;
  justify-content: space-between;
  perspective: 2000px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
[data-rwp] {
  position: relative;
  overflow: hidden;
  flex: 1;
  cursor: default;
  -webkit-mask-image:
    linear-gradient(
      to bottom,
      transparent 0%,
      black 20%,
      black 80%,
      transparent 100%);
  mask-image:
    linear-gradient(
      to bottom,
      transparent 0%,
      black 20%,
      black 80%,
      transparent 100%);
}
[data-rwp-highlight-wrapper] {
  position: absolute;
  overflow: hidden;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
  font-size: 1rem;
  font-weight: 500;
}
[data-rwp-highlight-list] {
  position: absolute;
  width: 100%;
  list-style: none;
}
[data-rwp-options] {
  position: absolute;
  top: 50%;
  left: 0;
  display: block;
  width: 100%;
  height: 0;
  margin: 0 auto;
  -webkit-font-smoothing: subpixel-antialiased;
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  list-style: none;
}
[data-rwp-option] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  -webkit-font-smoothing: subpixel-antialiased;
  will-change: visibility;
  font-size: .875rem;
}
[data-rwp-option],
[data-rwp-highlight-item] {
  display: flex;
  align-items: center;
  justify-content: center;
}
