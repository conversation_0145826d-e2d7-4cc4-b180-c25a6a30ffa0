// components/teacher/TeacherClassesPage.client.tsx
'use client';

import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';

const TeacherClassesPage = () => {
  const { slug } = useParams();
  const { user } = useAuth();
  
  // Verify the slug matches the current user
  if (user && user.slug !== slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You can only access your own profile.</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold">Loading...</h1>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Recent Activities</h1>
      <div className="space-y-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md border-l-4 border-green-500">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-semibold text-lg">Grade Reports Generated</h3>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Successfully generated grade reports for all Math classes
              </p>
              <span className="text-sm text-gray-500 mt-2 block">2 hours ago</span>
            </div>
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
              Completed
            </span>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md border-l-4 border-blue-500">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-semibold text-lg">Attendance Sync</h3>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Synchronized attendance data with school management system
              </p>
              <span className="text-sm text-gray-500 mt-2 block">4 hours ago</span>
            </div>
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
              Synced
            </span>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md border-l-4 border-yellow-500">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-semibold text-lg">Assignment Grading</h3>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Auto-graded 45 multiple choice assignments
              </p>
              <span className="text-sm text-gray-500 mt-2 block">Yesterday</span>
            </div>
            <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">
              Processing
            </span>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md border-l-4 border-purple-500">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-semibold text-lg">Parent Notifications</h3>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Sent progress reports to 28 parents via email
              </p>
              <span className="text-sm text-gray-500 mt-2 block">2 days ago</span>
            </div>
            <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">
              Sent
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeacherClassesPage;
