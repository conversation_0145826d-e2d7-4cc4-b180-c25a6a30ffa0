type IconProps = React.HTMLAttributes<SVGElement>;

export const Icons = {
  GitHub: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="GitHub"
      role="img">
      <path
        d="M12 1.95068C17.525 1.95068 22 6.42568 22 11.9507C21.9995 14.0459 21.3419 16.0883 20.1198 17.7902C18.8977 19.4922 17.1727 20.768 15.1875 21.4382C14.6875 21.5382 14.5 21.2257 14.5 20.9632C14.5 20.6257 14.5125 19.5507 14.5125 18.2132C14.5125 17.2757 14.2 16.6757 13.8375 16.3632C16.0625 16.1132 18.4 15.2632 18.4 11.4257C18.4 10.3257 18.0125 9.43818 17.375 8.73818C17.475 8.48818 17.825 7.46318 17.275 6.08818C17.275 6.08818 16.4375 5.81318 14.525 7.11318C13.725 6.88818 12.875 6.77568 12.025 6.77568C11.175 6.77568 10.325 6.88818 9.525 7.11318C7.6125 5.82568 6.775 6.08818 6.775 6.08818C6.225 7.46318 6.575 8.48818 6.675 8.73818C6.0375 9.43818 5.65 10.3382 5.65 11.4257C5.65 15.2507 7.975 16.1132 10.2 16.3632C9.9125 16.6132 9.65 17.0507 9.5625 17.7007C8.9875 17.9632 7.55 18.3882 6.65 16.8757C6.4625 16.5757 5.9 15.8382 5.1125 15.8507C4.275 15.8632 4.775 16.3257 5.125 16.5132C5.55 16.7507 6.0375 17.6382 6.15 17.9257C6.35 18.4882 7 19.5632 9.5125 19.1007C9.5125 19.9382 9.525 20.7257 9.525 20.9632C9.525 21.2257 9.3375 21.5257 8.8375 21.4382C6.8458 20.7752 5.11342 19.502 3.88611 17.799C2.65881 16.096 1.9989 14.0498 2 11.9507C2 6.42568 6.475 1.95068 12 1.95068Z"
        fill="currentColor"
      />
    </svg>
  ),
  Star: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="Star"
      role="img">
      <path
        d="M11.5288 2.29524C11.7179 1.90159 12.2821 1.90159 12.4712 2.29524L14.9458 7.44648C15.0219 7.60491 15.1735 7.71434 15.3487 7.73728L21.0456 8.4832C21.4809 8.5402 21.6552 9.07312 21.3368 9.37342L17.1693 13.303C17.0412 13.4238 16.9832 13.6009 17.0154 13.7735L18.0616 19.3857C18.1416 19.8146 17.6852 20.144 17.2993 19.9359L12.249 17.2133C12.0937 17.1296 11.9063 17.1296 11.751 17.2133L6.70073 19.9359C6.3148 20.144 5.85841 19.8146 5.93837 19.3857L6.98459 13.7735C7.01677 13.6009 6.95885 13.4238 6.83068 13.303L2.66324 9.37342C2.34476 9.07312 2.51909 8.5402 2.95444 8.4832L8.65127 7.73728C8.82648 7.71434 8.97811 7.60491 9.05422 7.44648L11.5288 2.29524Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  Terminal: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="Terminal"
      role="img">
      <path
        d="M5 7L9.29289 11.2929C9.68342 11.6834 9.68342 12.3166 9.29289 12.7071L5 17M13 17H19"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  Quote: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="Quote"
      role="img">
      <path
        d="M9.99263 9.5C9.99263 11.433 8.42728 13 6.49631 13C4.56535 13 3 11.433 3 9.5C3 7.567 4.56535 6 6.49631 6C8.42728 6 9.99263 7.567 9.99263 9.5ZM9.99263 9.5C10.2424 14.25 7.99473 16 4.99789 18M20.981 9.5C20.981 11.433 19.4157 13 17.4847 13C15.5538 13 13.9884 11.433 13.9884 9.5C13.9884 7.567 15.5538 6 17.4847 6C19.4157 6 20.981 7.567 20.981 9.5ZM20.981 9.5C21.2308 14.25 18.9832 16 15.9863 18"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  Discord: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="Discord"
      role="img">
      <path
        d="M19.6361 5.02282C18.1907 4.35757 16.6648 3.88563 15.0973 3.61903C14.8828 4.00449 14.6888 4.40107 14.5159 4.80712C12.8463 4.5542 11.1484 4.5542 9.47881 4.80712C9.30587 4.40111 9.1118 4.00453 8.8974 3.61903C7.32897 3.88788 5.80205 4.36095 4.35518 5.0263C1.48276 9.29853 0.70409 13.4647 1.09342 17.5716C2.77558 18.821 4.6584 19.7713 6.66003 20.3809C7.11074 19.7716 7.50956 19.1251 7.85226 18.4483C7.20135 18.204 6.57311 17.9024 5.9748 17.5473C6.13227 17.4325 6.28627 17.3142 6.43508 17.1994C8.17601 18.0224 10.0761 18.4491 12 18.4491C13.9238 18.4491 15.8239 18.0224 17.5648 17.1994C17.7154 17.3229 17.8694 17.4412 18.0251 17.5473C17.4257 17.903 16.7963 18.2051 16.1442 18.4501C16.4865 19.1265 16.8853 19.7724 17.3364 20.3809C19.3398 19.7737 21.224 18.8239 22.9065 17.5734C23.3633 12.8106 22.1261 8.68274 19.6361 5.02282ZM8.34541 15.0459C7.26047 15.0459 6.36414 14.0561 6.36414 12.8384C6.36414 11.6208 7.22932 10.6223 8.34195 10.6223C9.45458 10.6223 10.344 11.6208 10.325 12.8384C10.3059 14.0561 9.45112 15.0459 8.34541 15.0459ZM15.6545 15.0459C14.5678 15.0459 13.675 14.0561 13.675 12.8384C13.675 11.6208 14.5401 10.6223 15.6545 10.6223C16.7689 10.6223 17.6514 11.6208 17.6323 12.8384C17.6133 14.0561 16.7602 15.0459 15.6545 15.0459Z"
        fill="currentColor"
      />
    </svg>
  ),
  Sun: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="Sun"
      role="img">
      <path
        d="M12 3V2M12 22V21M18.3598 5.64005L19.0698 4.93005M4.93016 19.07L5.64016 18.36M21 12H22M2 12H3M18.3598 18.36L19.0698 19.07M4.93016 4.93005L5.64016 5.64005M15.5355 8.46447C17.4882 10.4171 17.4882 13.5829 15.5355 15.5355C13.5829 17.4882 10.4171 17.4882 8.46447 15.5355C6.51185 13.5829 6.51185 10.4171 8.46447 8.46447C10.4171 6.51185 13.5829 6.51185 15.5355 8.46447Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  Moon: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="Moon"
      role="img">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.0931 2.53021C12.2873 2.86472 12.2712 3.28123 12.0517 3.59971C11.3858 4.56582 10.9961 5.73577 10.9961 6.99976C10.9961 10.3135 13.6824 12.9998 16.9961 12.9998C18.2602 12.9998 19.4302 12.61 20.3963 11.9441C20.7148 11.7246 21.1313 11.7084 21.4658 11.9026C21.8003 12.0968 21.9929 12.4664 21.9602 12.8519C21.5264 17.9745 17.2324 21.996 11.9981 21.996C6.47632 21.996 2 17.5197 2 11.9979C2 6.76372 6.0214 2.46985 11.1438 2.03581C11.5292 2.00315 11.8989 2.19569 12.0931 2.53021ZM9.42003 4.42417C6.268 5.49679 4 8.48285 4 11.9979C4 16.4151 7.58088 19.996 11.9981 19.996C15.5133 19.996 18.4994 17.7279 19.5719 14.5757C18.7632 14.8506 17.8967 14.9998 16.9961 14.9998C12.5778 14.9998 8.99609 11.418 8.99609 6.99976C8.99609 6.0993 9.14518 5.2328 9.42003 4.42417Z"
        fill="currentColor"
      />
      <path
        d="M16.2374 5.01736L17.049 3.39419C17.2332 3.02567 17.7591 3.02567 17.9434 3.3942L18.755 5.01736C18.8034 5.11413 18.8818 5.19259 18.9786 5.24097L20.6017 6.05255C20.9703 6.23682 20.9703 6.76272 20.6017 6.94698L18.9786 7.75857C18.8818 7.80695 18.8034 7.88541 18.755 7.98217L17.9434 9.60534C17.7591 9.97386 17.2332 9.97386 17.049 9.60534L16.2374 7.98217C16.189 7.88541 16.1105 7.80695 16.0138 7.75856L14.3906 6.94698C14.0221 6.76272 14.0221 6.23682 14.3906 6.05255L16.0138 5.24097C16.1105 5.19259 16.189 5.11413 16.2374 5.01736Z"
        fill="currentColor"
      />
    </svg>
  ),
  System: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="System"
      role="img">
      <path
        d="M9 17H5C3.89543 17 3 16.1046 3 15V7C3 5.89543 3.89543 5 5 5H19C20.1046 5 21 5.89543 21 7V15C21 16.1046 20.1046 17 19 17H15M9 17V20H15V17M9 17H15"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="square"
        strokeLinejoin="round"
      />
    </svg>
  ),
  Menu: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="Menu"
      role="img">
      <path
        d="M3 12H21M3 6H21M3 18H21"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  Close: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="Close"
      role="img">
      <path
        d="M5 5L19 19M19 5L5 19"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
      />
    </svg>
  ),
  Check: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="Check"
      role="img">
      <path
        d="M5 12.75L10 19L19 5"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  Copy: (props: IconProps) => (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="Copy"
      role="img">
      <path
        d="M16 16.8V11.2C16 10.0799 16 9.51984 15.782 9.09202C15.5903 8.71569 15.2843 8.40973 14.908 8.21799C14.4802 8 13.9201 8 12.8 8H7.2C6.0799 8 5.51984 8 5.09202 8.21799C4.71569 8.40973 4.40973 8.71569 4.21799 9.09202C4 9.51984 4 10.0799 4 11.2V16.8C4 17.9201 4 18.4802 4.21799 18.908C4.40973 19.2843 4.71569 19.5903 5.09202 19.782C5.51984 20 6.0799 20 7.2 20H12.8C13.9201 20 14.4802 20 14.908 19.782C15.2843 19.5903 15.5903 19.2843 15.782 18.908C16 18.4802 16 17.9201 16 16.8Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <path
        d="M16 16H16.8C17.9201 16 18.4802 16 18.908 15.782C19.2843 15.5903 19.5903 15.2843 19.782 14.908C20 14.4802 20 13.9201 20 12.8V7.2C20 6.0799 20 5.51984 19.782 5.09202C19.5903 4.71569 19.2843 4.40973 18.908 4.21799C18.4802 4 17.9201 4 16.8 4H11.2C10.0799 4 9.51984 4 9.09202 4.21799C8.71569 4.40973 8.40973 4.71569 8.21799 5.09202C8 5.51984 8 6.0799 8 7.2V8"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
      />
    </svg>
  ),
};