// components/UserProfile.example.tsx
// This is an example component showing how to use the new type system

import React from 'react';
import { useAuth } from '../hooks/useAuth';
import type { 
  ExtendedUser, 
  StudentUser, 
  TeacherUser, 
  SchoolAdminUser 
} from '../lib/types';
import { 
  getUserDisplayName, 
  formatUserRole, 
  getRoleColorScheme,
  getStudentProperties,
  getTeacherProperties,
  getSchoolAdminProperties
} from '../lib/types';

interface UserProfileProps {
  showDetails?: boolean;
}

const UserProfile: React.FC<UserProfileProps> = ({ showDetails = false }) => {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated || !user) {
    return (
      <div className="p-4 bg-gray-100 rounded-lg">
        <p className="text-gray-600">Please sign in to view your profile.</p>
      </div>
    );
  }

  const displayName = getUserDisplayName(user);
  const roleDisplay = formatUserRole(user.role);
  const colorScheme = getRoleColorScheme(user.role);

  return (
    <div className={`p-6 bg-${colorScheme.secondary} rounded-lg border border-${colorScheme.primary}`}>
      <div className="flex items-center space-x-4">
        {user.image ? (
          <img 
            src={user.image} 
            alt={displayName}
            className="w-12 h-12 rounded-full"
          />
        ) : (
          <div className={`w-12 h-12 bg-${colorScheme.primary} rounded-full flex items-center justify-center`}>
            <span className="text-white font-semibold text-lg">
              {displayName.charAt(0).toUpperCase()}
            </span>
          </div>
        )}
        
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{displayName}</h3>
          <p className={`text-sm text-${colorScheme.primary}`}>{roleDisplay}</p>
          <p className="text-xs text-gray-500">{user.email}</p>
        </div>
      </div>

      {showDetails && <UserRoleDetails user={user} />}
    </div>
  );
};

// Component to show role-specific details with type safety
const UserRoleDetails: React.FC<{ user: ExtendedUser }> = ({ user }) => {
  const studentProps = getStudentProperties(user);
  const teacherProps = getTeacherProperties(user);
  const schoolAdminProps = getSchoolAdminProperties(user);

  if (studentProps) {
    return <StudentDetails student={studentProps} />;
  }

  if (teacherProps) {
    return <TeacherDetails teacher={teacherProps} />;
  }

  if (schoolAdminProps) {
    return <SchoolAdminDetails schoolAdmin={schoolAdminProps} />;
  }

  return null;
};

const StudentDetails: React.FC<{ student: StudentUser }> = ({ student }) => (
  <div className="mt-4 pt-4 border-t border-green-200">
    <h4 className="font-medium text-green-800 mb-2">Student Information</h4>
    <div className="space-y-1 text-sm">
      <p><span className="font-medium">Status:</span> {student.isActive ? 'Active' : 'Inactive'}</p>
      {student.grade && <p><span className="font-medium">Grade:</span> {student.grade}</p>}
      {student.classId && <p><span className="font-medium">Class ID:</span> {student.classId}</p>}
      <p><span className="font-medium">Teacher ID:</span> {student.teacherId}</p>
    </div>
  </div>
);

const TeacherDetails: React.FC<{ teacher: TeacherUser }> = ({ teacher }) => (
  <div className="mt-4 pt-4 border-t border-blue-200">
    <h4 className="font-medium text-blue-800 mb-2">Teacher Information</h4>
    <div className="space-y-1 text-sm">
      <p><span className="font-medium">Status:</span> {teacher.isVerified ? 'Verified' : 'Pending Verification'}</p>
      <p><span className="font-medium">Type:</span> {teacher.isIndependent ? 'Independent' : 'School-affiliated'}</p>
      {teacher.department && <p><span className="font-medium">Department:</span> {teacher.department}</p>}
      {teacher.subjects && (
        <p><span className="font-medium">Subjects:</span> {teacher.subjects}</p>
      )}
      {teacher.schoolId && <p><span className="font-medium">School ID:</span> {teacher.schoolId}</p>}
    </div>
  </div>
);

const SchoolAdminDetails: React.FC<{ schoolAdmin: SchoolAdminUser }> = ({ schoolAdmin }) => (
  <div className="mt-4 pt-4 border-t border-purple-200">
    <h4 className="font-medium text-purple-800 mb-2">School Administration</h4>
    <div className="space-y-1 text-sm">
      {schoolAdmin.schoolName && <p><span className="font-medium">School:</span> {schoolAdmin.schoolName}</p>}
      {schoolAdmin.schoolType && <p><span className="font-medium">Type:</span> {schoolAdmin.schoolType}</p>}
      {schoolAdmin.schoolDistrict && <p><span className="font-medium">District:</span> {schoolAdmin.schoolDistrict}</p>}
      {schoolAdmin.studentCount !== undefined && (
        <p><span className="font-medium">Students:</span> {schoolAdmin.studentCount}</p>
      )}
      {schoolAdmin.teacherCount !== undefined && (
        <p><span className="font-medium">Teachers:</span> {schoolAdmin.teacherCount}</p>
      )}
      {schoolAdmin.schoolAddress && (
        <p><span className="font-medium">Address:</span> {schoolAdmin.schoolAddress}</p>
      )}
    </div>
  </div>
);

export default UserProfile;