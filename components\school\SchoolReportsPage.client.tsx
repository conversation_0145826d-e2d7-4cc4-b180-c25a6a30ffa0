// components/school/SchoolReportsPage.client.tsx
'use client';

import { useState } from 'react';
import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';

const SchoolReportsPage = () => {
  const { slug } = useParams();
  const { user } = useAuth();
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  
  // Verify the slug matches the current user
  if (user && user.slug !== slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You can only access your own school profile.</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold">Loading...</h1>
      </div>
    );
  }

  const reports = [
    {
      id: 'attendance',
      title: 'Attendance Report',
      description: 'Monthly attendance statistics for all students',
      type: 'Monthly',
      lastGenerated: '2024-01-15',
      status: 'Ready'
    },
    {
      id: 'grades',
      title: 'Grade Distribution Report',
      description: 'Academic performance across all subjects and grades',
      type: 'Quarterly',
      lastGenerated: '2024-01-10',
      status: 'Ready'
    },
    {
      id: 'enrollment',
      title: 'Enrollment Report',
      description: 'Student enrollment trends and demographics',
      type: 'Annual',
      lastGenerated: '2024-01-01',
      status: 'Ready'
    },
    {
      id: 'teacher-performance',
      title: 'Teacher Performance Report',
      description: 'Teaching effectiveness and student feedback analysis',
      type: 'Quarterly',
      lastGenerated: '2024-01-08',
      status: 'Processing'
    },
    {
      id: 'financial',
      title: 'Financial Summary',
      description: 'Budget allocation and expenditure analysis',
      type: 'Monthly',
      lastGenerated: '2024-01-12',
      status: 'Ready'
    },
    {
      id: 'discipline',
      title: 'Discipline Report',
      description: 'Student behavior incidents and disciplinary actions',
      type: 'Monthly',
      lastGenerated: '2024-01-14',
      status: 'Ready'
    }
  ];

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">School Reports</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
          Generate Custom Report
        </button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Reports</h3>
          <p className="text-2xl font-bold text-blue-600">{reports.length}</p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Ready</h3>
          <p className="text-2xl font-bold text-green-600">
            {reports.filter(r => r.status === 'Ready').length}
          </p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Processing</h3>
          <p className="text-2xl font-bold text-yellow-600">
            {reports.filter(r => r.status === 'Processing').length}
          </p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">This Month</h3>
          <p className="text-2xl font-bold text-purple-600">12</p>
        </div>
      </div>

      {/* Reports Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {reports.map((report) => (
          <div key={report.id} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {report.title}
              </h3>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                report.status === 'Ready' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {report.status}
              </span>
            </div>
            
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
              {report.description}
            </p>
            
            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
              <span>Type: {report.type}</span>
              <span>Last: {report.lastGenerated}</span>
            </div>
            
            <div className="flex space-x-2">
              <button 
                className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 disabled:opacity-50"
                disabled={report.status === 'Processing'}
              >
                {report.status === 'Processing' ? 'Processing...' : 'View Report'}
              </button>
              <button className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-700">
                Download
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
        <h3 className="text-lg font-semibold mb-4">Recent Report Activity</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
            <div>
              <p className="font-medium">Attendance Report generated</p>
              <p className="text-sm text-gray-500">Monthly report for January 2024</p>
            </div>
            <span className="text-sm text-gray-500">2 hours ago</span>
          </div>
          
          <div className="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
            <div>
              <p className="font-medium">Financial Summary updated</p>
              <p className="text-sm text-gray-500">Budget allocation changes reflected</p>
            </div>
            <span className="text-sm text-gray-500">1 day ago</span>
          </div>
          
          <div className="flex items-center justify-between py-2">
            <div>
              <p className="font-medium">Grade Distribution Report completed</p>
              <p className="text-sm text-gray-500">Q4 2023 academic performance analysis</p>
            </div>
            <span className="text-sm text-gray-500">3 days ago</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SchoolReportsPage;
