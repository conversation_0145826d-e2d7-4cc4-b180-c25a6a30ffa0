/* CSS-only Squircle Implementation */
/* This provides squircle-like shapes without requiring CSS Paint Worklet */

:root {
  --squircle-bg: #3b82f6;
  --squircle-border: transparent;
  --squircle-border-width: 0;
  --squircle-radius: 20px;
}

/* Base squircle class with CSS fallback */
.squircle-css {
  background: var(--squircle-background-color, var(--squircle-bg));
  border: var(--squircle-border-width, 0) solid var(--squircle-border-color, var(--squircle-border));
  border-radius: calc(var(--squircle-border-radius, var(--squircle-radius)) * 0.6);
  position: relative;
}

/* Enhanced squircle using multiple box-shadows for better curve approximation */
.squircle-enhanced {
  background: var(--squircle-background-color, var(--squircle-bg));
  border: var(--squircle-border-width, 0) solid var(--squircle-border-color, var(--squircle-border));
  border-radius: calc(var(--squircle-border-radius, var(--squircle-radius)) * 0.7);
  position: relative;
}

.squircle-enhanced::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: inherit;
  border-radius: calc(var(--squircle-border-radius, var(--squircle-radius)) * 0.8);
  z-index: -1;
  opacity: 0.3;
}

/* Squircle with clip-path for modern browsers */
.squircle-clip {
  background: var(--squircle-background-color, var(--squircle-bg));
  border: var(--squircle-border-width, 0) solid var(--squircle-border-color, var(--squircle-border));
  clip-path: polygon(
    0% calc(var(--squircle-border-radius, var(--squircle-radius)) * 1.2),
    calc(var(--squircle-border-radius, var(--squircle-radius)) * 0.8) 0%,
    calc(100% - calc(var(--squircle-border-radius, var(--squircle-radius)) * 0.8)) 0%,
    100% calc(var(--squircle-border-radius, var(--squircle-radius)) * 1.2),
    100% calc(100% - calc(var(--squircle-border-radius, var(--squircle-radius)) * 1.2)),
    calc(100% - calc(var(--squircle-border-radius, var(--squircle-radius)) * 0.8)) 100%,
    calc(var(--squircle-border-radius, var(--squircle-radius)) * 0.8) 100%,
    0% calc(100% - calc(var(--squircle-border-radius, var(--squircle-radius)) * 1.2))
  );
}

/* SVG-based squircle for perfect curves */
.squircle-svg {
  background: var(--squircle-background-color, var(--squircle-bg));
  border: var(--squircle-border-width, 0) solid var(--squircle-border-color, var(--squircle-border));
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M20,0 L80,0 Q100,0 100,20 L100,80 Q100,100 80,100 L20,100 Q0,100 0,80 L0,20 Q0,0 20,0 Z' fill='white'/%3E%3C/svg%3E");
  mask-size: 100% 100%;
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M20,0 L80,0 Q100,0 100,20 L100,80 Q100,100 80,100 L20,100 Q0,100 0,80 L0,20 Q0,0 20,0 Z' fill='white'/%3E%3C/svg%3E");
  -webkit-mask-size: 100% 100%;
}

/* Utility classes for common squircle sizes */
.squircle-sm {
  --squircle-border-radius: 12px;
}

.squircle-md {
  --squircle-border-radius: 20px;
}

.squircle-lg {
  --squircle-border-radius: 32px;
}

.squircle-xl {
  --squircle-border-radius: 48px;
}

/* Color utilities */
.squircle-blue {
  --squircle-background-color: #3b82f6;
}

.squircle-green {
  --squircle-background-color: #10b981;
}

.squircle-red {
  --squircle-background-color: #ef4444;
}

.squircle-purple {
  --squircle-background-color: #8b5cf6;
}

.squircle-yellow {
  --squircle-background-color: #f59e0b;
}

/* Responsive squircle */
@media (max-width: 768px) {
  .squircle-responsive {
    --squircle-border-radius: 16px;
  }
}

@media (min-width: 769px) {
  .squircle-responsive {
    --squircle-border-radius: 24px;
  }
}

/* Animation support */
.squircle-animated {
  transition: all 0.3s ease;
}

.squircle-animated:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

/* Default fallback for the original squircle class */
.squircle {
  background: var(--squircle-background-color, var(--squircle-bg));
  border: var(--squircle-border-width, 0) solid var(--squircle-border-color, var(--squircle-border));
  border-radius: calc(var(--squircle-border-radius, var(--squircle-radius)) * 0.65);
}