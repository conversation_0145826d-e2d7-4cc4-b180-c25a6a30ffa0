// components/student/StudentToDoPage.client.tsx
'use client';

import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';
import { CheckCircle, Clock, AlertCircle, BookOpen, FileText, Calendar } from 'lucide-react';
import { cn } from '../../lib/utils';

// Mock data for assignments - in a real app, this would come from the server
const mockAssignments = [
  {
    id: '1',
    title: 'Math Homework - Chapter 5',
    subject: 'Mathematics',
    dueDate: '2024-01-25',
    priority: 'high',
    status: 'pending',
    description: 'Complete exercises 1-20 from Chapter 5: Algebra Basics'
  },
  {
    id: '2',
    title: 'Science Lab Report',
    subject: 'Science',
    dueDate: '2024-01-27',
    priority: 'medium',
    status: 'in-progress',
    description: 'Write a lab report on the photosynthesis experiment'
  },
  {
    id: '3',
    title: 'English Essay Draft',
    subject: 'English',
    dueDate: '2024-01-30',
    priority: 'low',
    status: 'pending',
    description: 'First draft of essay on "To Kill a Mockingbird"'
  },
  {
    id: '4',
    title: 'History Timeline Project',
    subject: 'History',
    dueDate: '2024-02-02',
    priority: 'medium',
    status: 'completed',
    description: 'Create a timeline of World War II events'
  }
];

const StudentToDoPage = () => {
  const { slug } = useParams();
  const { user } = useAuth();
  
  // Verify the slug matches the current user
  if (user && user.slug !== slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You can only access your own profile.</p>
        </div>
      </div>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'in-progress': return <Clock className="w-5 h-5 text-yellow-600" />;
      case 'pending': return <AlertCircle className="w-5 h-5 text-red-600" />;
      default: return <FileText className="w-5 h-5 text-gray-600" />;
    }
  };

  const pendingAssignments = mockAssignments.filter(a => a.status !== 'completed');
  const completedAssignments = mockAssignments.filter(a => a.status === 'completed');

  return (
    <div className="min-h-screen bg-[#f2f2f2] dark:bg-[#0d0d0d] pt-20 pb-24">
      <div className="max-w-6xl mx-auto px-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-black dark:text-white mb-2">
            To Do
          </h1>
          <p className="text-black/60 dark:text-white/60">
            Keep track of your assignments and tasks
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10">
            <div className="flex items-center gap-3">
              <AlertCircle className="w-8 h-8 text-red-600" />
              <div>
                <h3 className="text-2xl font-bold text-black dark:text-white">
                  {pendingAssignments.length}
                </h3>
                <p className="text-black/60 dark:text-white/60">Pending Tasks</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10">
            <div className="flex items-center gap-3">
              <Clock className="w-8 h-8 text-yellow-600" />
              <div>
                <h3 className="text-2xl font-bold text-black dark:text-white">
                  {mockAssignments.filter(a => a.status === 'in-progress').length}
                </h3>
                <p className="text-black/60 dark:text-white/60">In Progress</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <div>
                <h3 className="text-2xl font-bold text-black dark:text-white">
                  {completedAssignments.length}
                </h3>
                <p className="text-black/60 dark:text-white/60">Completed</p>
              </div>
            </div>
          </div>
        </div>

        {/* Pending Assignments */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-black dark:text-white mb-4">
            Pending Assignments
          </h2>
          <div className="space-y-4">
            {pendingAssignments.map((assignment) => (
              <div
                key={assignment.id}
                className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    {getStatusIcon(assignment.status)}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-black dark:text-white mb-1">
                        {assignment.title}
                      </h3>
                      <p className="text-black/60 dark:text-white/60 mb-2">
                        {assignment.description}
                      </p>
                      <div className="flex items-center gap-4 text-sm">
                        <span className="text-black/60 dark:text-white/60">
                          Subject: {assignment.subject}
                        </span>
                        <span className="text-black/60 dark:text-white/60">
                          Due: {new Date(assignment.dueDate).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  <span className={cn(
                    "px-3 py-1 rounded-full text-xs font-medium border",
                    getPriorityColor(assignment.priority)
                  )}>
                    {assignment.priority.charAt(0).toUpperCase() + assignment.priority.slice(1)} Priority
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Completed Assignments */}
        {completedAssignments.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold text-black dark:text-white mb-4">
              Recently Completed
            </h2>
            <div className="space-y-4">
              {completedAssignments.map((assignment) => (
                <div
                  key={assignment.id}
                  className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10 opacity-75"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4 flex-1">
                      {getStatusIcon(assignment.status)}
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-black dark:text-white mb-1 line-through">
                          {assignment.title}
                        </h3>
                        <p className="text-black/60 dark:text-white/60 mb-2">
                          {assignment.description}
                        </p>
                        <div className="flex items-center gap-4 text-sm">
                          <span className="text-black/60 dark:text-white/60">
                            Subject: {assignment.subject}
                          </span>
                          <span className="text-green-600">
                            ✓ Completed
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentToDoPage;
