// types/squircle.d.ts
declare module '@squircle/paint-polyfill' {
  export function polyfill(): Promise<void>;
  export default function polyfill(): Promise<void>;
}

declare module '@squircle/core' {
  export function init(): Promise<void>;
  export default function init(): Promise<void>;
}

// Extend CSS namespace for paint worklet
declare global {
  interface CSS {
    paintWorklet?: {
      addModule(url: string): Promise<void>;
    };
  }

  interface Window {
    CSS: CSS;
  }
}