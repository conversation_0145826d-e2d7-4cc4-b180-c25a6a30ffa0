import { useCookie } from 'blade/hooks';
import { useCallback, useEffect, useState } from 'react';
import { WiMoonAltNew } from "react-icons/wi";

import BorderDark from './ui/border-dark.client';
import BorderLight from './ui/border-light.client';

export type Theme = 'light' | 'dark' | 'system';

interface ThemeToggleProps {
  initial?: Theme | null;
  disableTooltips?: boolean;
}



export function ThemeToggle(props: ThemeToggleProps) {
  const [currentTheme, setCurrentTheme] = useState<Theme>(props.initial ?? 'system');
  const [themeCookie, setThemeCookie] = useCookie<Theme>('theme');

  // Initialize theme state from cookie or props
  useEffect(() => {
    if (themeCookie) {
      setCurrentTheme(themeCookie);
    } else if (props.initial) {
      setCurrentTheme(props.initial);
    }
  }, [themeCookie, props.initial]);

  // Apply theme to document when currentTheme changes
  useEffect(() => {
    // Apply theme class immediately for instant feedback
    // The layout.tsx will also apply it via useMetadata, but this ensures immediate response
    if (currentTheme === 'dark') {
      document.documentElement.classList.add('dark');
      document.documentElement.classList.remove('light');
    } else if (currentTheme === 'light') {
      document.documentElement.classList.remove('dark');
      document.documentElement.classList.add('light');
    } else {
      // System mode - remove both classes to let CSS media queries handle it
      document.documentElement.classList.remove('dark');
      document.documentElement.classList.remove('light');
    }
  }, [currentTheme]);

  const setTheme = useCallback(
    (theme: Theme): void => {
      setCurrentTheme(theme);
      setThemeCookie(theme, { client: true });

      // Update localStorage
      if (theme === 'system') {
        localStorage.removeItem('theme');
        localStorage.removeItem('darkMode');
      } else {
        localStorage.setItem('theme', theme);
        localStorage.setItem('darkMode', theme);
      }
    },
    [setThemeCookie],
  );



  return (
    <button
      type="button"
      className="cursor-pointer rounded-md p-1.5 text-muted-foreground transition duration-200 hover:bg-accent hover:text-primary hover:duration-0"
      onClick={() =>
        setTheme(
          currentTheme === 'light'
            ? 'dark'
            : currentTheme === 'dark'
              ? 'system'
              : 'light',
        )
      }>
      {currentTheme === 'dark' ? (
        <BorderLight component={() => <WiMoonAltNew className="w-4 h-4" style={{ color: '#f2f2f2' } as React.CSSProperties} />} />
      ) : currentTheme === 'light' ? (
        <BorderDark component={() => <WiMoonAltNew className="w-4 h-4" style={{ color: '#070707' } as React.CSSProperties} />} />
      ) : (
        <WiMoonAltNew className="w-4 h-4" />
      )}
    </button>
  );
}