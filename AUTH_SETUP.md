# Authentication System Setup

This document explains the multi-role authentication system implemented in this Blade.js application using Better Auth.

## Overview

The application supports three distinct user roles with different authentication methods:

1. **Students**: Username-only authentication (usernames created by teachers)
2. **Teachers**: Email/password, Email OTP, or Google OAuth authentication
3. **School Administrators**: Email OTP authentication only

## Architecture

### Authentication Flow

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   LoginForm     │    │  Role-specific   │    │   Dashboard     │
│   (Unified)     │───▶│  Auth Clients    │───▶│   (Protected)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### File Structure

```
lib/
├── auth.ts                 # Common auth types
├── auth-client.ts          # Unified auth client
├── student/
│   ├── auth.ts            # Student auth server config
│   └── auth-client.ts     # Student auth client
├── teacher/
│   ├── auth.ts            # Teacher auth server config
│   └── auth-client.ts     # Teacher auth client
└── school/
    ├── auth.ts            # School auth server config
    └── auth-client.ts     # School auth client

components/
└── LoginForm.client.tsx    # Unified login form

pages/
├── login.tsx              # Login page
├── student/
│   ├── layout.tsx         # Protected student layout
│   └── dashboard.tsx      # Student dashboard
├── teacher/
│   ├── layout.tsx         # Protected teacher layout
│   └── dashboard.tsx      # Teacher dashboard
└── school/
    ├── layout.tsx         # Protected school layout
    └── dashboard.tsx      # School dashboard

hooks/
└── useAuth.tsx            # Unified auth hook

triggers/
├── user.ts                # User management triggers
└── session.ts             # Session management triggers
```

## Authentication Methods

### Students
- **Method**: Username only
- **Creation**: Usernames are created by teachers
- **Login**: Simple username input
- **Features**: No password required, managed by teachers

### Teachers
- **Methods**: 
  - Email + Password
  - Email OTP (One-Time Password)
  - Google OAuth
- **Signup**: Self-registration available
- **Features**: Can create and manage student accounts

### School Administrators
- **Method**: Email OTP only
- **Signup**: Self-registration with school verification
- **Features**: Can manage teachers and school-wide settings

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Database
BLADE_RONIN_TOKEN="your-ronin-token"

# Better Auth
BLADE_BETTER_AUTH_SECRET="your-secret-key"
BLADE_BETTER_AUTH_URL="http://localhost:3000"

# Email Service (Resend)
BLADE_RESEND_API_KEY="your-resend-api-key"

# Google OAuth (for teachers)
BLADE_GOOGLE_CLIENT_ID="your-google-client-id"
BLADE_GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Google Places API (for school search)
BLADE_GOOGLE_PLACES_API_KEY="your-google-places-api-key"
```

### 2. Database Schema

The system uses RONIN database with Better Auth adapter. Required tables:
- `user` - User accounts
- `session` - User sessions
- `account` - OAuth accounts
- `verification` - Email verification tokens

### 3. API Routes

The router.ts handles authentication endpoints:
- `/api/auth/student/*` - Student authentication
- `/api/auth/teacher/*` - Teacher authentication  
- `/api/auth/school/*` - School administrator authentication
- `/api/auth/session` - Unified session endpoint
- `/api/auth/sign-out` - Unified sign out

## Usage

### Login Flow

1. User visits `/login`
2. Selects their role (student/teacher/school_admin)
3. Completes role-specific authentication
4. Redirected to appropriate dashboard

### URL Parameters

You can direct users to specific login flows:
- `/login?role=student` - Direct to student login
- `/login?role=teacher` - Direct to teacher login
- `/login?role=school_admin` - Direct to school admin login

### Protected Routes

Each role has protected routes that automatically redirect unauthorized users:

```typescript
// Example: Teacher-only route
const { user, loading } = useAuth();

if (!user || user.role !== 'teacher') {
  redirect('/login?role=teacher');
}
```

## Components

### LoginForm.client.tsx

Unified login form that handles all three authentication flows:
- Role selection
- Credential input (role-specific)
- OTP verification (when applicable)

### useAuth Hook

Provides unified authentication state:

```typescript
const { user, loading, signOut, role, isAuthenticated } = useAuth();
```

### Protected Layouts

Each role has a protected layout that:
- Checks authentication status
- Validates user role
- Provides role-specific navigation
- Handles loading states

## Security Features

1. **Role-based Access Control**: Each route validates user role
2. **Session Management**: Automatic session validation
3. **CSRF Protection**: Built into Better Auth
4. **Secure Cookies**: HTTP-only session cookies
5. **Email Verification**: OTP-based email verification
6. **Rate Limiting**: Built into Better Auth plugins

## Development

### Adding New Roles

1. Create new auth configuration in `lib/[role]/`
2. Add role-specific routes in router.ts
3. Create protected layout in `pages/[role]/`
4. Update LoginForm.client.tsx
5. Add role to TypeScript types

### Testing Authentication

1. Start the development server: `npm run dev`
2. Visit `/login` to test the unified login flow
3. Test each role's authentication method
4. Verify protected route access control

## Troubleshooting

### Common Issues

1. **Email not sending**: Check RESEND_API_KEY configuration
2. **Google OAuth not working**: Verify Google OAuth credentials and redirect URLs
3. **Session not persisting**: Check Better Auth secret and cookie settings
4. **Database errors**: Verify RONIN_TOKEN and database schema

### Debug Mode

Enable debug logging by setting:
```bash
DEBUG=better-auth:*
```

## API Reference

### Authentication Endpoints

- `POST /api/auth/student/sign-in` - Student username login
- `POST /api/auth/teacher/sign-in` - Teacher email/password login
- `POST /api/auth/teacher/email-otp/send` - Send teacher OTP
- `POST /api/auth/teacher/email-otp/verify` - Verify teacher OTP
- `GET /api/auth/teacher/google` - Teacher Google OAuth
- `POST /api/auth/school/email-otp/send` - Send school admin OTP
- `POST /api/auth/school/email-otp/verify` - Verify school admin OTP

### Session Management

- `GET /api/auth/session` - Get current session
- `POST /api/auth/sign-out` - Sign out user

## Best Practices

1. Always use the unified `useAuth` hook for authentication state
2. Implement proper loading states in protected routes
3. Use role-specific redirects for better UX
4. Validate user roles on both client and server
5. Handle authentication errors gracefully
6. Keep authentication logic centralized in hooks and layouts