# Blade Authentication Examples

This document shows practical examples of how to use the authentication system following <PERSON>'s philosophy of instant rendering and no loading states.

## Basic Usage Examples

### 1. **Using useAuth Hook**

```typescript
// In any component
import { useAuth } from '../hooks/useAuth';

const MyComponent = () => {
  const { user, isAuthenticated, canAccessRoles } = useAuth();
  
  // Instant access to user state - no loading checks needed
  if (!isAuthenticated) {
    return <div>Please log in to continue</div>;
  }
  
  return (
    <div>
      <h1>Welcome, {user.name}!</h1>
      <p>Role: {user.role}</p>
    </div>
  );
};
```

### 2. **Protecting Components with withRouteProtection**

```typescript
// Wrap entire components for role-based access
import { withRouteProtection } from '../utils/route-protection';

const TeacherOnlyComponent = () => {
  return <div>This is only for teachers!</div>;
};

// Export the protected version
export default withRouteProtection(TeacherOnlyComponent, ['teacher']);
```

### 3. **Conditional Content with ProtectedContent**

```typescript
import { ProtectedContent } from '../utils/route-protection';

const Dashboard = () => {
  return (
    <div>
      <h1>Dashboard</h1>
      
      {/* Content for all authenticated users */}
      <ProtectedContent>
        <div>Welcome to your dashboard!</div>
      </ProtectedContent>
      
      {/* Teacher-only content */}
      <ProtectedContent 
        allowedRoles={['teacher']}
        fallback={<div>Teacher features coming soon!</div>}
      >
        <div>Teacher-specific tools and features</div>
      </ProtectedContent>
      
      {/* Admin content for teachers and school admins */}
      <ProtectedContent allowedRoles={['teacher', 'school_admin']}>
        <div>Administrative tools</div>
      </ProtectedContent>
    </div>
  );
};
```

### 4. **Route Access Checking**

```typescript
import { useCanAccessRoute } from '../utils/route-protection';

const NavigationMenu = () => {
  const canAccessTeacher = useCanAccessRoute('/teacher');
  const canAccessStudent = useCanAccessRoute('/student');
  const canAccessSchool = useCanAccessRoute('/school');
  
  return (
    <nav>
      {canAccessStudent && (
        <a href="/student/dashboard">Student Dashboard</a>
      )}
      {canAccessTeacher && (
        <a href="/teacher/dashboard">Teacher Dashboard</a>
      )}
      {canAccessSchool && (
        <a href="/school/dashboard">School Dashboard</a>
      )}
    </nav>
  );
};
```

### 5. **Role-based Helper Methods**

```typescript
import { useAuth } from '../hooks/useAuth';

const FeatureToggle = () => {
  const { canAccessRoles } = useAuth();
  
  return (
    <div>
      {canAccessRoles(['teacher']) && (
        <button>Create Assignment</button>
      )}
      
      {canAccessRoles(['teacher', 'school_admin']) && (
        <button>View Reports</button>
      )}
      
      {canAccessRoles(['school_admin']) && (
        <button>Manage School</button>
      )}
    </div>
  );
};
```

## Advanced Examples

### 6. **Custom Protected Page**

```typescript
// pages/teacher/advanced-tools.tsx
import { withRouteProtection } from '../../utils/route-protection';

const AdvancedToolsPage = () => {
  return (
    <div>
      <h1>Advanced Teaching Tools</h1>
      <p>These tools are only available to teachers.</p>
    </div>
  );
};

// Protect the entire page
export default withRouteProtection(AdvancedToolsPage, ['teacher']);
```

### 7. **Multi-role Component**

```typescript
import { useAuth, ProtectedContent } from '../utils/route-protection';

const ClassroomComponent = () => {
  const { user } = useAuth();
  
  return (
    <div>
      <h2>Classroom: Math 101</h2>
      
      {/* Different views based on role */}
      <ProtectedContent allowedRoles={['student']}>
        <div>
          <h3>Student View</h3>
          <p>Your assignments and grades</p>
        </div>
      </ProtectedContent>
      
      <ProtectedContent allowedRoles={['teacher']}>
        <div>
          <h3>Teacher View</h3>
          <p>Manage assignments and grade students</p>
        </div>
      </ProtectedContent>
      
      <ProtectedContent allowedRoles={['school_admin']}>
        <div>
          <h3>Admin View</h3>
          <p>Classroom analytics and teacher performance</p>
        </div>
      </ProtectedContent>
    </div>
  );
};
```

### 8. **Smart Redirects in Components**

```typescript
import { useAuth } from '../hooks/useAuth';
import { useRedirect } from 'blade/hooks';
import { useEffect } from 'react';

const SmartComponent = () => {
  const { user, isAuthenticated } = useAuth();
  const redirect = useRedirect();
  
  useEffect(() => {
    // Custom redirect logic
    if (isAuthenticated && user?.role === 'student') {
      // Students should go to a specific page
      redirect('/student/assignments');
    }
  }, [user, isAuthenticated, redirect]);
  
  if (!isAuthenticated) {
    return <div>Please log in</div>;
  }
  
  return <div>Content for authenticated users</div>;
};
```

## Key Principles

### ✅ **Do This (Blade Philosophy)**

```typescript
// ✅ Instant rendering, no loading states
const Component = () => {
  const { user } = useAuth();
  
  if (!user) {
    return <LoginPrompt />;
  }
  
  return <AuthenticatedContent />;
};

// ✅ Use ProtectedContent for conditional rendering
<ProtectedContent allowedRoles={['teacher']}>
  <TeacherTools />
</ProtectedContent>

// ✅ Use withRouteProtection for entire components
export default withRouteProtection(MyPage, ['teacher']);
```

### ❌ **Don't Do This**

```typescript
// ❌ Don't use loading states
const Component = () => {
  const { user, loading } = useAuth();
  
  if (loading) {
    return <Spinner />; // Avoid this!
  }
  
  return <Content />;
};

// ❌ Don't manually check loading in every component
const Component = () => {
  const { user, loading } = useAuth();
  
  if (loading) return <div>Loading...</div>; // Blade handles this
  
  return <div>Content</div>;
};
```

## Route Configuration

```typescript
// utils/route-protection.ts
export const routeConfig: RouteConfig[] = [
  // Public routes (redirect authenticated users away)
  { path: '/', type: 'public' },
  { path: '/login', type: 'public' },
  
  // Protected routes (require authentication + specific roles)
  { path: '/student', type: 'protected', allowedRoles: ['student'] },
  { path: '/teacher', type: 'protected', allowedRoles: ['teacher'] },
  { path: '/school', type: 'protected', allowedRoles: ['school_admin'] },
];
```

## Testing Examples

```typescript
// Test access control in your components
const TestComponent = () => {
  const { canAccessRoles } = useAuth();
  const canAccessTeacher = useCanAccessRoute('/teacher');
  
  return (
    <div>
      <p>Can access teacher routes: {canAccessTeacher ? 'Yes' : 'No'}</p>
      <p>Can access teacher content: {canAccessRoles(['teacher']) ? 'Yes' : 'No'}</p>
    </div>
  );
};
```

This system provides **enterprise-grade security** while maintaining Blade's philosophy of instant, joyful user experiences without loading states.