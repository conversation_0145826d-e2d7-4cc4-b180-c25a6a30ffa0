// pages/student/layout.tsx (Server Component - NO hooks allowed)
import { LayoutWrapper } from '../../components/auth/student/dual-sidebar';

const StudentLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <LayoutWrapper
      showUserAvatar={true}
      showTopNav={true}
      enableRightSidebar={false}
    >
      {children}
    </LayoutWrapper>
  );
};

export default StudentLayout;