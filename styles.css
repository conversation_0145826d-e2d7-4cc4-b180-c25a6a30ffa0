@import "tailwindcss";
@import '@squircle/tailwindcss';
@import "tw-animate-css";
@import "./styles/squircle-fallback.css";

@custom-variant dark {
    &:where(.dark, .dark *) {
        @slot;
    }

    @media (prefers-color-scheme: dark) {
        &:not(:where(.light, .light *)) {
            @slot;
        }
    }
}

.root {
  isolation: isolate;
}

/* Hide browser scrollbar for authenticated routes that use dual-sidebar with custom scrollbars */
.hide-browser-scrollbar {
  overflow: hidden !important;
  height: 100vh !important;
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

/* WebKit browsers (Chrome, Safari, Opera) */
.hide-browser-scrollbar::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Apply to html and body elements when they have the class */
html.hide-browser-scrollbar,
body.hide-browser-scrollbar {
  overflow: hidden !important;
  height: 100vh !important;
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

html.hide-browser-scrollbar::-webkit-scrollbar,
body.hide-browser-scrollbar::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Style browser scrollbar for public routes to match custom-scrollbar design */
.styled-browser-scrollbar {
  /* Firefox scrollbar styling - defaults to light mode */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-light) transparent;

  /* Mobile touch scrolling improvements */
  -webkit-overflow-scrolling: touch;

  /* Ensure smooth scrolling on all devices */
  scroll-behavior: smooth;

  /* Mobile-specific touch improvements */
  touch-action: pan-y;
  overscroll-behavior: contain;
}

/* WebKit scrollbar styling for public routes (Desktop Safari, Chrome) */
.styled-browser-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px; /* For horizontal scrollbars */
}

.styled-browser-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-light);
  border-radius: 3px;
  transition: background 0.2s ease;

  /* Enhanced mobile touch target */
  min-height: 20px;
}

.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-light);
}

.styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-light);
}

/* Hide scrollbar corner */
.styled-browser-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Enhanced gradient styles for light mode */
.styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-light);
  border: 1px solid var(--scrollbar-thumb-border-light);
}

/* Apply to html and body elements when they have the styled-browser-scrollbar class */
html.styled-browser-scrollbar,
body.styled-browser-scrollbar {
  /* Firefox scrollbar styling - defaults to light mode */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-light) transparent;

  /* Mobile touch scrolling improvements */
  -webkit-overflow-scrolling: touch;

  /* Ensure smooth scrolling on all devices */
  scroll-behavior: smooth;

  /* Mobile-specific touch improvements */
  touch-action: pan-y;
  overscroll-behavior: contain;
}

/* WebKit scrollbar styling for html and body elements */
html.styled-browser-scrollbar::-webkit-scrollbar,
body.styled-browser-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

html.styled-browser-scrollbar::-webkit-scrollbar-track,
body.styled-browser-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

html.styled-browser-scrollbar::-webkit-scrollbar-thumb,
body.styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-light);
  border: 1px solid var(--scrollbar-thumb-border-light);
  border-radius: 3px;
  transition: background 0.2s ease;
  min-height: 20px;
}

html.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover,
body.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-light);
}

html.styled-browser-scrollbar::-webkit-scrollbar-thumb:active,
body.styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-light);
}

html.styled-browser-scrollbar::-webkit-scrollbar-corner,
body.styled-browser-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Dark mode styles for styled browser scrollbar */
@media (prefers-color-scheme: dark) {
  .styled-browser-scrollbar {
    scrollbar-color: var(--scrollbar-thumb-dark) transparent;
  }
  
  .styled-browser-scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-dark);
  }
  
  .styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover-dark);
  }
  
  .styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
    background: var(--scrollbar-thumb-active-dark);
  }
  
  .styled-browser-scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-gradient-dark);
    border: 1px solid var(--scrollbar-thumb-border-dark);
  }

  html.styled-browser-scrollbar,
  body.styled-browser-scrollbar {
    scrollbar-color: var(--scrollbar-thumb-dark) transparent;
  }
  
  html.styled-browser-scrollbar::-webkit-scrollbar-thumb,
  body.styled-browser-scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-gradient-dark);
    border: 1px solid var(--scrollbar-thumb-border-dark);
  }
  
  html.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover,
  body.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover-dark);
  }
  
  html.styled-browser-scrollbar::-webkit-scrollbar-thumb:active,
  body.styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
    background: var(--scrollbar-thumb-active-dark);
  }
}

/* If you're using a class-based theme system (like Tailwind's dark class) */
.dark .styled-browser-scrollbar {
  scrollbar-color: var(--scrollbar-thumb-dark) transparent;
}

.dark .styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-dark);
}

.dark .styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-dark);
}

.dark .styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-dark);
}

.dark .styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-dark);
  border: 1px solid var(--scrollbar-thumb-border-dark);
}

.dark html.styled-browser-scrollbar,
.dark body.styled-browser-scrollbar {
  scrollbar-color: var(--scrollbar-thumb-dark) transparent;
}

.dark html.styled-browser-scrollbar::-webkit-scrollbar-thumb,
.dark body.styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-dark);
  border: 1px solid var(--scrollbar-thumb-border-dark);
}

.dark html.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover,
.dark body.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-dark);
}

.dark html.styled-browser-scrollbar::-webkit-scrollbar-thumb:active,
.dark body.styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-dark);
}

/* Mobile-specific improvements for styled browser scrollbar */
@media (max-width: 768px) {
  .styled-browser-scrollbar {
    /* Hide scrollbars on mobile but keep functionality */
    scrollbar-width: none;
    -ms-overflow-style: none;

    /* Essential mobile scrolling properties */
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;
    overscroll-behavior: contain;

    /* Add momentum scrolling for iOS */
    -webkit-scroll-behavior: smooth;
    scroll-behavior: smooth;

    /* Ensure proper overflow behavior */
    overflow-y: auto !important;
    overflow-x: hidden !important;

    /* Fix for iOS Safari scrolling issues */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);

    /* Prevent scroll bounce on iOS */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }

  .styled-browser-scrollbar::-webkit-scrollbar,
  html.styled-browser-scrollbar::-webkit-scrollbar,
  body.styled-browser-scrollbar::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }

  html.styled-browser-scrollbar,
  body.styled-browser-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;
    overscroll-behavior: contain;
    -webkit-scroll-behavior: smooth;
    scroll-behavior: smooth;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }
}

:root {
  --main: oklch(0.97 0 0);
  --main-secondary: oklch(97% 0 0);
  --main-muted: oklch(0.96 0 0);
 
  --primary: oklch(0 0 0);
  --primary-foreground: oklch(37.1% 0 0);
  --primary-muted: oklch(0.3 0 0);
 
  --border: oklch(0.885 0 0);
}
 
.dark {
  --main: oklch(0.178 0 0);
  --main-secondary: oklch(0.205 0 0);
  --main-muted: oklch(0.168 0 0);
 
  --primary: oklch(1 0 0);
  --primary-foreground: oklch(0.97 0 0);
  --primary-muted: oklch(0.85 0 0);
 
  --border: oklch(0.26 0 0);
}
 

@layer base {
  html {
    color-scheme: light dark;
  }

  * {
    border-color: hsl(var(--border));
  }

  html,
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  .using-mouse * {
    outline: none !important;
  }

  /* Manrope Font Family */
  @font-face {
    font-family: "manrope_1";
    src: url("/fonts/Manrope-Regular.woff2") format("woff2"),
         url("/fonts/Manrope-Regular.ttf") format("truetype");
    font-display: swap;
    font-weight: 400;
    font-style: normal;
  }

  @font-face {
    font-family: "manrope_1";
    src: url("/fonts/Manrope-Medium.woff2") format("woff2");
    font-display: swap;
    font-weight: 500;
    font-style: normal;
  }

  @font-face {
    font-family: "manrope_1";
    src: url("/fonts/Manrope-SemiBold.woff2") format("woff2");
    font-display: swap;
    font-weight: 600;
    font-style: normal;
  }

  @font-face {
    font-family: "manrope_1";
    src: url("/fonts/Manrope-Bold.woff2") format("woff2");
    font-display: swap;
    font-weight: 700;
    font-style: normal;
  }

   @font-face {
    font-family: "redaction-regular";
    src: url("/fonts/Redaction-Regular.woff2") format("woff2");
    font-display: swap;
    font-weight: 400;
    font-style: normal;
  }

   @font-face {
    font-family: "redaction";
    src: url("/fonts/Redaction-Bold.woff2") format("woff2");
    font-display: swap;
    font-weight: 700;
    font-style: normal;
  }

   @font-face {
    font-family: "redaction-italic";
    src: url("/fonts/Redaction-Italic.woff2") format("woff2");
    font-display: swap;
    font-weight: 500;
    font-style: normal;
  }

      /* Debug: Force font loading test */
  .font-redaction-regular {
    font-family: "redaction-regular",  system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  }
    /* Debug: Force font loading test */
  .font-redaction {
    font-family: "redaction",  system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  }
  /* Debug: Force font loading test */
  .font-manrope_1 {
    font-family: "manrope_1",  system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  }
}
@layer base {
  :root {
    --sidebar: oklch(255.255 255 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);
  }

  .dark {
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.439 0 0);
  }
}

@theme inline {
  --animate-spotlight: spotlight 2s ease 0.75s 1 forwards;
}
 
@keyframes spotlight {
  0% {
    opacity: 0;
    transform: translate(-72%, -62%) scale(0.5);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -40%) scale(1);
  }
}

@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 3s ease infinite;
}




:root {
  --scrollbar-thumb-light: rgba(0, 0, 0, 0.15);
  --scrollbar-thumb-hover-light: rgba(0, 0, 0, 0.25);
  --scrollbar-thumb-active-light: rgba(0, 0, 0, 0.35);
  --scrollbar-thumb-gradient-light: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.2) 100%);
  --scrollbar-thumb-border-light: rgba(0, 0, 0, 0.05);
  
  --scrollbar-thumb-dark: rgba(255, 255, 255, 0.15);
  --scrollbar-thumb-hover-dark: rgba(255, 255, 255, 0.25);
  --scrollbar-thumb-active-dark: rgba(255, 255, 255, 0.35);
  --scrollbar-thumb-gradient-dark: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 100%);
  --scrollbar-thumb-border-dark: rgba(255, 255, 255, 0.05);
}



.custom-scrollbar {
  /* Firefox scrollbar styling - defaults to light mode */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-light) transparent;

  /* Mobile touch scrolling improvements */
  -webkit-overflow-scrolling: touch;

  /* Ensure smooth scrolling on all devices */
  scroll-behavior: smooth;

  /* Mobile-specific touch improvements */
  touch-action: pan-y;
  overscroll-behavior: contain;
}

/* WebKit scrollbar styling (Desktop Safari, Chrome) */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px; /* For horizontal scrollbars */
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-light);
  border-radius: 3px;
  transition: background 0.2s ease;

  /* Enhanced mobile touch target */
  min-height: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-light);
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-light);
}

/* Hide scrollbar corner */
.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Enhanced gradient styles for light mode */
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-light);
  border: 1px solid var(--scrollbar-thumb-border-light);
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .custom-scrollbar {
    scrollbar-color: var(--scrollbar-thumb-dark) transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-dark);
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover-dark);
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:active {
    background: var(--scrollbar-thumb-active-dark);
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-gradient-dark);
    border: 1px solid var(--scrollbar-thumb-border-dark);
  }
}

/* If you're using a class-based theme system (like Tailwind's dark class) */
.dark .custom-scrollbar {
  scrollbar-color: var(--scrollbar-thumb-dark) transparent;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-dark);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-dark);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-dark);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-dark);
  border: 1px solid var(--scrollbar-thumb-border-dark);
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  .custom-scrollbar {
    /* Hide scrollbars on mobile but keep functionality */
    scrollbar-width: none;
    -ms-overflow-style: none;

    /* Essential mobile scrolling properties */
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;
    overscroll-behavior: contain;

    /* Add momentum scrolling for iOS */
    -webkit-scroll-behavior: smooth;
    scroll-behavior: smooth;

    /* Ensure proper overflow behavior */
    overflow-y: auto !important;
    overflow-x: hidden !important;

    /* Fix for iOS Safari scrolling issues */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);

    /* Prevent scroll bounce on iOS */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }

  .custom-scrollbar::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }

  /* Specific fixes for flyout containers */
  [data-flyout-panel="true"] .custom-scrollbar,
  .flyout-content .custom-scrollbar {
    /* Force hardware acceleration */
    will-change: scroll-position;
    transform: translate3d(0, 0, 0);

    /* Ensure proper height calculation */
    height: 100%;
    max-height: 100%;

    /* Fix touch scrolling in nested containers */
    position: relative;
    z-index: 1;

    /* Additional iOS Safari fixes */
    -webkit-transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000;
  }

  /* Ensure flyout panels themselves allow scrolling */
  [data-flyout-panel="true"] {
    /* Enable proper touch scrolling for the entire flyout */
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;
  }

  /* Critical fix: Ensure scrollable content inside flyouts works */
  [data-flyout-panel="true"] .custom-scrollbar,
  .flyout-content .custom-scrollbar {
    /* Override any parent touch-action restrictions */
    touch-action: pan-y !important;

    /* Ensure the scrollable area is properly sized */
    min-height: 0;
    flex: 1;

    /* Force proper scrolling behavior */
    overflow-y: auto !important;
    overflow-x: hidden !important;

    /* iOS specific fixes */
    -webkit-overflow-scrolling: touch !important;

    /* Prevent any interference from parent containers */
    isolation: isolate;
  }
}


/* Mobile touch improvements for interactive elements */
@media (max-width: 768px) {
  /* Improve touch targets for buttons and interactive elements */
  button, [role="button"], [data-radix-collection-item] {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    touch-action: manipulation;
    min-height: 44px; /* iOS recommended minimum touch target */
    min-width: 44px;
  }

  /* Specific improvements for segmented controls */
  [data-radix-tabs-trigger] {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    touch-action: manipulation;
    cursor: pointer;
  }

  /* Ensure proper touch behavior for toggle switches */
  [role="switch"], .toggle-switch {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    touch-action: manipulation;
    cursor: pointer;
  }
}

/* Squircle styles */
.squircle {
  background: paint(squircle);
  background-repeat: no-repeat;
  --squircle-border-width: 0;
  --squircle-border-radius: 12px;
  --squircle-border-smoothing: 0.6;
}

.squircle-mask {
  mask-image: paint(squircle);
  mask-repeat: no-repeat;
  --squircle-mode: mask-image;
}

/* Fallback for browsers without paint support */
@supports not (background: paint(squircle)) {
  .squircle-xs { border-radius: 4px; }
  .squircle-sm { border-radius: 6px; }
  .squircle-md { border-radius: 8px; }
  .squircle-lg { border-radius: 12px; }
  .squircle-xl { border-radius: 16px; }
  .squircle-2xl { border-radius: 20px; }
  .squircle-3xl { border-radius: 24px; }
}

/* Border mask support */
.squircle-mask {
  mask-image: paint(squircle);
  mask-repeat: no-repeat;
}

/* Default squircle properties */
.squircle, .squircle-mask {
  --squircle-border-width: 0;
  --squircle-border-color: transparent;
}

/* Active state styles */
.squircle-active {
  --squircle-border-width: 1.5px;
  --squircle-border-color: #3e3e44;
}

/* Hover animations */
.squircle {
  transition: all 0.2s ease-in-out;
}

.squircle:hover {
  --squircle-background-color: #2a2a2e;
}

/* Improved squircle base styles */
.squircle {
  background: paint(squircle);
  background-repeat: no-repeat;
  --squircle-border-width: 0;
  --squircle-border-radius: 12px;
  --squircle-border-smoothing: 0.8;
  position: relative;
  overflow: hidden;
}

/* Smooth scale utilities */
.scale-102 {
  transform: scale(1.02);
}

.scale-105 {
  transform: scale(1.05);
}

/* Custom easing for smooth transitions */
.transition-smooth {
  transition: all 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* Enhanced glassmorphism effect */
.glass-morphism {
  backdrop-filter: blur(24px) saturate(180%);
  -webkit-backdrop-filter: blur(24px) saturate(180%);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.35) 0%,
    rgba(255, 255, 255, 0.20) 30%,
    rgba(255, 255, 255, 0.15) 70%,
    rgba(255, 255, 255, 0.08) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 
    /* Inner highlights */
    inset 0 1px 1px rgba(255, 255, 255, 0.6),
    inset 0 -1px 1px rgba(255, 255, 255, 0.2),
    /* Outer shadows */
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 6px 20px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.08),
    /* Subtle glow */
    0 0 30px rgba(255, 255, 255, 0.08);
}

/* Hover effects for non-active buttons */
.icon-button-hover {
  transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.icon-button-hover:hover:not(.active) {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Active state animation */
@keyframes gentle-pulse {
  0%, 100% { 
    box-shadow: 
      inset 0 1px 1px rgba(255, 255, 255, 0.6),
      inset 0 -1px 1px rgba(255, 255, 255, 0.2),
      0 12px 40px rgba(0, 0, 0, 0.15),
      0 0 30px rgba(255, 255, 255, 0.08);
  }
  50% { 
    box-shadow: 
      inset 0 1px 1px rgba(255, 255, 255, 0.7),
      inset 0 -1px 1px rgba(255, 255, 255, 0.3),
      0 16px 48px rgba(0, 0, 0, 0.18),
      0 0 36px rgba(255, 255, 255, 0.12);
  }
}

.icon-active-animation {
  animation: gentle-pulse 3s ease-in-out infinite;
}

/* Backdrop blur fallback for better browser support */
@supports not (backdrop-filter: blur(1px)) {
  .glass-morphism {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.4) 0%,
      rgba(255, 255, 255, 0.25) 30%,
      rgba(255, 255, 255, 0.2) 70%,
      rgba(255, 255, 255, 0.15) 100%
    );
  }
}

/* Squircle enhancements */
.squircle-enhanced {
  --squircle-border-smoothing: 0.85;
  transform-style: preserve-3d;
  will-change: transform, opacity;
}

/* Smooth indicator dot */
.indicator-dot {
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0.95) 50%,
    rgba(255, 255, 255, 0.8) 100%
  );
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
}

.sidebar-glass {
  background: linear-gradient(
    180deg,
    rgba(20, 20, 23, 0.95) 0%,
    rgba(20, 20, 23, 0.98) 50%,
    rgba(20, 20, 23, 0.95) 100%
  );
  backdrop-filter: blur(20px) saturate(120%);
  -webkit-backdrop-filter: blur(20px) saturate(120%);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  
  /* Add subtle noise texture */
  background-image: 
    linear-gradient(
      180deg,
      rgba(20, 20, 23, 0.95) 0%,
      rgba(20, 20, 23, 0.98) 50%,
      rgba(20, 20, 23, 0.95) 100%
    ),
    radial-gradient(
      circle at 20% 20%,
      rgba(255, 255, 255, 0.02) 0%,
      transparent 50%
    );
}

@layer demo {
  .noise {
    --lines: 0.0003px;

    /*
      repeating sub pixel radial hard stop lines
      creates thousands, so small they distort
      use this distortion as a mask
      the hard stops are basically on/off lines
    */
    mask: repeating-radial-gradient(
      circle at center,
      #000,
      var(--lines),
      #000,
      0, /* transition hints make code easier to manage */
      #0000,
      calc(var(--lines) * 2),
      #0000 0 /* trailing 0 is part of the hard stop logic */
    );

    --space:;
    @supports (background: linear-gradient(in oklch, #000, #000)) {
      --space: in oklch;
    }

    display: grid;

    /* shared gradient across headlines "fixed" */
    background: linear-gradient(to bottom right var(--space), var(--primary), var(--primary-muted)) fixed;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

/* Override Radix UI Sheet animations for mobile */
.mobile-sheet-content {
  /* Disable Radix default animations */
  animation: none !important;
  transform: translateX(-100%) !important;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.mobile-sheet-content[data-state="open"] {
  transform: translateX(0) !important;
}

.mobile-sheet-content[data-state="closed"] {
  transform: translateX(-100%) !important;
}

/* Ensure smooth transitions */
.mobile-sheet-content * {
  transition-property: none !important;
}

/* Alternative approach - if the above doesn't work, try these keyframes */
@keyframes mobile-flyout-in {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes mobile-flyout-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}
/* Add this to your global CSS file */

/* Mobile sheet with smooth transitions and no blinking */
.mobile-sheet-content {
  /* Disable Radix default animations */
  animation: none !important;
  transform: translateX(-100%) !important;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  /* Ensure consistent layering */
  z-index: 10 !important;
}

.mobile-sheet-content[data-state="open"] {
  transform: translateX(0) !important;
}

.mobile-sheet-content[data-state="closed"] {
  transform: translateX(-100%) !important;
}

/* Prevent any child elements from causing flickering */
.mobile-sheet-content * {
  transition-property: none !important;
}

/* Ensure the icon rail stays above the flyout */
.sidebar-glass {
  /* Force hardware acceleration for smoother rendering */
  transform: translateZ(0);
  backface-visibility: hidden;
  /* Ensure it stays above everything */
  z-index: 30 !important;
}

/* Prevent any z-index changes during transitions */
.sidebar-glass .group,
.sidebar-glass button {
  /* Maintain consistent z-index */
  z-index: inherit;
  /* Hardware acceleration */
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Smooth icon transitions without blinking */
.sidebar-glass button {
  /* Prevent any layout shifts */
  will-change: transform;
  /* Ensure smooth scaling */
  transition: transform 0.5s ease-out !important;
}

/* Alternative approach - if still having issues, use this */
@media (max-width: 768px) {
  /* Force the icon rail to stay on top during mobile transitions */
  .sidebar-glass {
    position: fixed !important;
    z-index: 100 !important;
    /* Prevent any transform changes */
    transform: none !important;
  }

  /* Ensure flyout is below icon rail */
  .mobile-sheet-content {
    z-index: 50 !important;
  }
}

/* Toast z-index overrides - ensure toasts appear above all flyouts and modals */
[data-sonner-toaster] {
  z-index: 999999999999 !important;
}

[data-sonner-toast] {
  z-index: 999999999999 !important;
}

/* Additional toast styling for dark mode consistency */
[data-sonner-toaster][data-theme="dark"] {
  --normal-bg: transparent !important;
  --normal-border: transparent !important;
  --normal-text: white !important;
}

/* Ensure toast content has proper z-index */
.group\/toast {
  z-index: 999999999999 !important;
  position: fixed !important;
}

/* Override any potential z-index conflicts */
.group\/toast * {
  z-index: inherit !important;
}

/* Primary color variables */
:root {
--primary: oklch(0 0 0);
--primary-muted: oklch(0.3 0 0);
--primary-invert: oklch(1 0 0);
}

.dark {
--primary: oklch(1 0 0);
--primary-invert: oklch(0 0 0);
--primary-muted: oklch(0.85 0 0);
}

@theme inline {
--color-primary: var(--primary);
--color-primary-muted: var(--primary-muted);
--color-primary-invert: var(--primary-invert);
}

/* Main color variables */
:root {
  --main: oklch(0.97 0 0);
  --main-secondary: oklch(97% 0 0);
  --main-foreground: oklch(0.925 0 0);
  --main-muted: oklch(0.96 0 0);
  --main-background: oklch(0.97 0 0);
  --main-invert: oklch(0.205 0 0);
  
  --primary: oklch(0 0 0);
  --primary-foreground: oklch(37.1% 0 0);
  --primary-muted: oklch(0.3 0 0);
  --primary-invert: oklch(1 0 0);
  
  --border: oklch(0.885 0 0);
  }
  
  .dark {
  --main: oklch(0.178 0 0);
  --main-secondary: oklch(0.205 0 0);
  --main-foreground: oklch(0.269 0 0);
  --main-muted: oklch(0.168 0 0);
  --main-background: oklch(0.145 0 0);
  --main-invert: oklch(0.8 0 0);
  
  --primary: oklch(1 0 0);
  --primary-invert: oklch(0 0 0);
  --primary-foreground: oklch(0.97 0 0);
  --primary-muted: oklch(0.85 0 0);
  
  --border: oklch(0.26 0 0);
  }
  
  @theme inline {
  --color-main: var(--main);
  --color-main-secondary: var(--main-secondary);
  --color-main-foreground: var(--main-foreground);
  --color-main-muted: var(--main-muted);
  --color-main-background: var(--main-background);
  --color-main-invert: var(--main-invert);
  
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary-muted: var(--primary-muted);
  --color-primary-invert: var(--primary-invert);
  
  --color-border: var(--border);
  
  --animate-shine: shine 6s linear infinite;
  --animate-brightness: brightness 2.2s linear infinite;
  
  @keyframes shine {
    from {
      background-position: 0 0;
    }
    to {
      background-position: -400% 0;
    }
  }
  
  @keyframes brightness {
    0% {
      transform: skew(-13deg) translateX(-100%);
    }
    100% {
      transform: skew(-13deg) translateX(100%);
    }
  }
  }

@keyframes fastPulse {
  0%, 100% { opacity: 1 }
  50% { opacity: 0.4 }
}

.fast-pulse {
  animation: fastPulse 0.6s ease-in-out infinite;
}

  @keyframes rotate {
    to {
      --angle: 360deg;
    }
  }

  @property --angle {
    syntax: "<angle>";
    initial-value: 0deg;
    inherits: false;
  }


input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}



.box_dark {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px;
  height: auto;
  width: auto;
  border: 2px solid #0000;
  border-radius: 100%;
  background: linear-gradient(#000000, #000000) padding-box, linear-gradient(
        var(--angle),
        #070707,
        #E47320
      ) border-box;
  animation: 8s rotate linear infinite;
}



.box_light {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px;
  height: auto;
  width: auto;
  border: 2px solid #0000;
  border-radius: 100%;
  background: linear-gradient(#ffffff, #ffffff) padding-box, linear-gradient(
        var(--angle),
        #f2f2f2,
        #E47320
      ) border-box;
  animation: 8s rotate linear infinite;
  }
