import type { ReactNode } from 'react';
import type { LucideIcon } from 'lucide-react';

export type UserRole = 'student' | 'teacher' | 'school';

export type SidebarMode = 'dual' | 'left-only' | 'right-only' | 'hidden';

export type ActionType = 'flyout' | 'navigate' | 'modal' | 'inline';

export interface SidebarAction {
  type: ActionType;
  target?: string; // Route for navigate, content key for flyout/modal
  component?: ReactNode; // Custom component for inline actions
}

export interface SidebarItem {
  id: string;
  label: string;
  icon: LucideIcon;
  action: SidebarAction;
  badge?: string | number;
  isActive?: boolean;
  children?: SidebarItem[];
  permissions?: UserRole[];
}

export interface SidebarSection {
  id: string;
  title?: string;
  items: SidebarItem[];
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export interface SidebarConfig {
  mode: SidebarMode;
  leftSidebar?: {
    sections: SidebarSection[];
    showHeader?: boolean;
    showFooter?: boolean;
    width?: 'icon' | 'compact' | 'full';
    allowFlyouts?: boolean;
  };
  rightSidebar?: {
    sections: SidebarSection[];
    showHeader?: boolean;
    showFooter?: boolean;
    defaultContent?: string;
    allowFlyouts?: boolean;
  };
  userNav?: {
    show: boolean;
    position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    items: SidebarItem[];
  };
  header?: {
    show: boolean;
    title?: string;
    actions?: SidebarItem[];
  };
  permissions: {
    canCreate?: boolean;
    canEdit?: boolean;
    canDelete?: boolean;
    canView?: boolean;
  };
}

export interface DynamicSidebarProps {
  config: SidebarConfig;
  userRole: UserRole;
  children: ReactNode;
  className?: string;
  onAction?: (action: SidebarAction, item: SidebarItem) => void;
}

// Predefined configurations for different user roles
export interface RoleConfigs {
  student: SidebarConfig;
  teacher: SidebarConfig;
  school: SidebarConfig;
}