// utils/auth.ts
// This file is kept for backward compatibility
// The main authentication logic has moved to:
// - hooks/useAuth.tsx (main auth hook)
// - utils/route-protection.ts (route protection)
// - lib/auth-client.ts (unified session management)

import { useAuth as useAuthHook } from '../hooks/useAuth';
import { useUnifiedSession } from '../lib/auth-client';

export interface UserPayload {
  id: string;
  role: 'student' | 'teacher' | 'school_admin';
  email: string;
  name: string;
  slug?: string;
  exp?: number;
}

// Legacy useAuth hook - redirects to the new implementation
export const useAuth = () => {
  const { user } = useAuthHook();
  return user;
};

// Deprecated: Use useRouteProtection from utils/route-protection.ts instead
export const useProtectedRoute = (allowedRoles: string[] = []) => {
  console.warn('useProtectedRoute is deprecated. Use useRouteProtection from utils/route-protection.ts instead');
  const { user, canAccessRoles } = useAuthHook();
  
  if (!user) {
    return { user: null, isAuthorized: false };
  }
  
  if (allowedRoles.length === 0) {
    return { user, isAuthorized: true };
  }
  
  const isAuthorized = canAccessRoles(allowedRoles);
  return { user, isAuthorized };
};

// Hook for getting the appropriate auth client based on role
export const useAuthClient = () => {
  const { session, signOut } = useUnifiedSession();
  return { session, signOut };
};
