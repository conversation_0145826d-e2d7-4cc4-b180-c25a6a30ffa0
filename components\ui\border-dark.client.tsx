interface BorderAnimationProps {
  component: () => React.ReactElement;
  className?: string;
}

function BorderDark({ component, className }: BorderAnimationProps) {
  return (
    <div
      className={`box_dark ${className || ""}`}
      style={{ pointerEvents: 'none' } as React.CSSProperties}
    >
      <div style={{ pointerEvents: 'auto' } as React.CSSProperties}>
        {component()}
      </div>
    </div>
  );
}

export default BorderDark;
