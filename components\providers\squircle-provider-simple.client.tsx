'use client';
import * as React from 'react';

// Simple CSS-only squircle styles injection
const injectSquircleStyles = () => {
  if (typeof document === 'undefined') return;
  
  // Check if styles are already injected
  if (document.getElementById('squircle-fallback-styles')) return;
  
  const style = document.createElement('style');
  style.id = 'squircle-fallback-styles';
  style.textContent = `
    /* CSS-only Squircle fallback styles */
    .squircle {
      background: var(--squircle-background-color, #3b82f6);
      border: var(--squircle-border-width, 0) solid var(--squircle-border-color, transparent);
      border-radius: calc(var(--squircle-border-radius, 20px) * 0.65);
      transition: all 0.2s ease;
    }
    
    .squircle-css {
      background: var(--squircle-background-color, #3b82f6);
      border: var(--squircle-border-width, 0) solid var(--squircle-border-color, transparent);
      border-radius: calc(var(--squircle-border-radius, 20px) * 0.6);
    }
    
    .squircle-enhanced {
      background: var(--squircle-background-color, #3b82f6);
      border: var(--squircle-border-width, 0) solid var(--squircle-border-color, transparent);
      border-radius: calc(var(--squircle-border-radius, 20px) * 0.7);
      position: relative;
    }
    
    .squircle-enhanced::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      background: inherit;
      border-radius: calc(var(--squircle-border-radius, 20px) * 0.8);
      z-index: -1;
      opacity: 0.3;
    }
    
    /* Size utilities */
    .squircle-sm { --squircle-border-radius: 12px; }
    .squircle-md { --squircle-border-radius: 20px; }
    .squircle-lg { --squircle-border-radius: 32px; }
    .squircle-xl { --squircle-border-radius: 48px; }
    
    /* Color utilities */
    .squircle-blue { --squircle-background-color: #3b82f6; }
    .squircle-green { --squircle-background-color: #10b981; }
    .squircle-red { --squircle-background-color: #ef4444; }
    .squircle-purple { --squircle-background-color: #8b5cf6; }
    .squircle-yellow { --squircle-background-color: #f59e0b; }
  `;
  
  document.head.appendChild(style);
  console.log('✅ Squircle CSS fallback styles injected');
};

// Attempt squircle worklet initialization (optional)
const trySquircleInit = async () => {
  if (typeof window === 'undefined') return false;
  
  try {
    // Quick timeout to avoid hanging
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Quick timeout')), 2000)
    );
    
    const { init } = await import('@squircle/core');
    await Promise.race([init(), timeoutPromise]);
    
    console.log('✅ Squircle worklet initialized');
    return true;
  } catch (error) {
    console.log('🎨 Squircle worklet failed, using CSS fallback');
    return false;
  }
};

interface SquircleProviderProps {
  children: React.ReactNode;
}

export function SquircleProviderSimple({ children }: SquircleProviderProps) {
  const [mounted, setMounted] = React.useState(false);
  
  React.useEffect(() => {
    setMounted(true);
    
    // Inject CSS styles immediately
    injectSquircleStyles();
    
    // Try worklet initialization in background (non-blocking)
    trySquircleInit().catch(() => {
      // Ignore errors - CSS fallback is already active
    });
  }, []);

  // Render children immediately - no blocking on squircle initialization
  return <>{children}</>;
}

// Hook for programmatic squircle usage
export const useSquircle = () => {
  return React.useCallback((element: HTMLElement | null, options: {
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: string;
    borderRadius?: string;
  } = {}) => {
    if (!element) return;

    // Apply CSS custom properties
    if (options.backgroundColor) {
      element.style.setProperty('--squircle-background-color', options.backgroundColor);
    }
    if (options.borderColor) {
      element.style.setProperty('--squircle-border-color', options.borderColor);
    }
    if (options.borderWidth) {
      element.style.setProperty('--squircle-border-width', options.borderWidth);
    }
    if (options.borderRadius) {
      element.style.setProperty('--squircle-border-radius', options.borderRadius);
    }

    // Add squircle class
    element.classList.add('squircle');
  }, []);
};

export default SquircleProviderSimple;