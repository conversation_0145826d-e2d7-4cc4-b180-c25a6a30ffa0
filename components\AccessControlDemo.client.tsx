// components/AccessControlDemo.client.tsx
'use client';

import { useAuth } from '../hooks/useAuth';
import { useCanAccessRoute, ProtectedContent } from '../utils/route-protection';

const AccessControlDemo = () => {
  const { user, isAuthenticated, canAccessRoles } = useAuth();
  
  const canAccessStudent = useCanAccessRoute('/student');
  const canAccessTeacher = useCanAccessRoute('/teacher');
  const canAccessSchool = useCanAccessRoute('/school');
  const canAccessPublic = useCanAccessRoute('/');

  return (
    <div className="space-y-6">
      {/* Public Access Demo */}
      <div className="p-6 bg-gray-100 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">Public Access Control</h3>
        {!isAuthenticated ? (
          <>
            <p className="text-gray-600 mb-4">Not authenticated - can only access public pages</p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <span className={canAccessPublic ? "text-green-600" : "text-red-600"}>
                  {canAccessPublic ? "✅" : "❌"}
                </span>
                <span>Homepage access</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-red-600">❌</span>
                <span>Student area access</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-red-600">❌</span>
                <span>Teacher area access</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-red-600">❌</span>
                <span>School admin area access</span>
              </div>
            </div>
          </>
        ) : (
          <p className="text-blue-600">✅ Authenticated - redirected away from public pages</p>
        )}
      </div>

      {/* Authenticated Access Demo */}
      <ProtectedContent fallback={
        <div className="p-6 bg-yellow-50 rounded-lg">
          <p className="text-yellow-800">🔒 This content is only visible to authenticated users</p>
        </div>
      }>
        <div className="p-6 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Authenticated Access Control</h3>
          <div className="mb-4">
            <p className="font-medium">Logged in as: {user?.name}</p>
            <p className="text-sm text-gray-600">Role: {user?.role}</p>
            <p className="text-sm text-gray-600">
              Current route status: {user?.isOnCorrectRoute?.() ? "✅ Correct area" : "⚠️ Wrong area"}
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <span className={canAccessPublic ? "text-green-600" : "text-red-600"}>
                {canAccessPublic ? "✅" : "❌"}
              </span>
              <span>Homepage access (should be ❌ when authenticated)</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className={canAccessStudent ? "text-green-600" : "text-red-600"}>
                {canAccessStudent ? "✅" : "❌"}
              </span>
              <span>Student area access</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className={canAccessTeacher ? "text-green-600" : "text-red-600"}>
                {canAccessTeacher ? "✅" : "❌"}
              </span>
              <span>Teacher area access</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className={canAccessSchool ? "text-green-600" : "text-red-600"}>
                {canAccessSchool ? "✅" : "❌"}
              </span>
              <span>School admin area access</span>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            <p>Correct dashboard URL: <code>{user?.getCorrectDashboardUrl?.()}</code></p>
          </div>
        </div>
      </ProtectedContent>

      {/* Role-based Content Demo */}
      <div className="space-y-4">
        <ProtectedContent 
          allowedRoles={['student']}
          fallback={<div className="p-4 bg-gray-100 rounded">🚫 Student-only content hidden</div>}
        >
          <div className="p-4 bg-green-100 rounded">
            🎓 This content is only visible to <strong>students</strong>
          </div>
        </ProtectedContent>

        <ProtectedContent 
          allowedRoles={['teacher']}
          fallback={<div className="p-4 bg-gray-100 rounded">🚫 Teacher-only content hidden</div>}
        >
          <div className="p-4 bg-blue-100 rounded">
            👨‍🏫 This content is only visible to <strong>teachers</strong>
          </div>
        </ProtectedContent>

        <ProtectedContent 
          allowedRoles={['school_admin']}
          fallback={<div className="p-4 bg-gray-100 rounded">🚫 Admin-only content hidden</div>}
        >
          <div className="p-4 bg-purple-100 rounded">
            🏫 This content is only visible to <strong>school administrators</strong>
          </div>
        </ProtectedContent>

        <ProtectedContent 
          allowedRoles={['teacher', 'school_admin']}
          fallback={<div className="p-4 bg-gray-100 rounded">🚫 Staff-only content hidden</div>}
        >
          <div className="p-4 bg-orange-100 rounded">
            👥 This content is visible to <strong>teachers and school administrators</strong>
          </div>
        </ProtectedContent>
      </div>

      {/* Access Methods Demo */}
      <ProtectedContent>
        <div className="p-6 bg-white border rounded-lg">
          <h4 className="font-medium mb-3">Role-based Access Methods:</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Can access teacher content:</span>
              <span className={canAccessRoles(['teacher']) ? "text-green-600" : "text-red-600"}>
                {canAccessRoles(['teacher']) ? "✅ Yes" : "❌ No"}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Can access admin content:</span>
              <span className={canAccessRoles(['teacher', 'school_admin']) ? "text-green-600" : "text-red-600"}>
                {canAccessRoles(['teacher', 'school_admin']) ? "✅ Yes" : "❌ No"}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Can access student content:</span>
              <span className={canAccessRoles(['student']) ? "text-green-600" : "text-red-600"}>
                {canAccessRoles(['student']) ? "✅ Yes" : "❌ No"}
              </span>
            </div>
          </div>
        </div>
      </ProtectedContent>
    </div>
  );
};

export default AccessControlDemo;