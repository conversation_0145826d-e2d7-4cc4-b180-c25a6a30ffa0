# .env
BLADE_RONIN_TOKEN=ronin-token

# Better Auth
BLADE_BETTER_AUTH_SECRET="your-better-auth-secret"
BLADE_BETTER_AUTH_URL=http://localhost:3000 # or your production URL
BLADE_PUBLIC_APP_URL=http://localhost:3000 # or your production URL

# Email Service (Resend)
BLADE_RESEND_API_KEY="your-resend-api-key"

# Google Places API (for school search)
BLADE_GOOGLE_PLACES_API_KEY="your-google-places-api-key"

# Social providers (if using)
BLADE_GITHUB_CLIENT_ID="GITHUB_CLIENT_ID"
BLADE_GITHUB_CLIENT_SECRET="GITHUB_CLIENT_SECRET"
BLADE_GOOGLE_CLIENT_ID="GOOGLE_CLIENT_ID"
BLADE_GOOGLE_CLIENT_SECRET="GOOGLE_CLIENT_SECRET"


