'use client';
import * as React from 'react';

// Enhanced squircle initialization with multiple fallback strategies
const initSquircleWithFallbacks = async () => {
  // Only run on client side
  if (typeof window === 'undefined') {
    return;
  }

  // Wait for document to be fully ready
  await new Promise<void>((resolve) => {
    if (document.readyState === 'complete') {
      resolve();
    } else {
      const handler = () => {
        resolve();
        window.removeEventListener('load', handler);
      };
      window.addEventListener('load', handler);
    }
  });

  // Additional wait to ensure all resources are loaded
  await new Promise(resolve => setTimeout(resolve, 200));

  const hasNativeSupport = 'paintWorklet' in CSS;
  console.log(`🎨 CSS Paint Worklet native support: ${hasNativeSupport}`);

  try {
    if (!hasNativeSupport) {
      console.log('🎨 Loading polyfill...');
      
      // Try multiple polyfill loading strategies
      try {
        // Strategy 1: Direct import
        await import('@squircle/paint-polyfill');
        console.log('✅ Polyfill loaded via direct import');
      } catch (directError) {
        console.warn('⚠️ Direct polyfill import failed:', directError);
        
        try {
          // Strategy 2: Dynamic script loading
          await loadPolyfillViaScript();
          console.log('✅ Polyfill loaded via script');
        } catch (scriptError) {
          console.error('❌ All polyfill loading strategies failed');
          throw new Error('Polyfill loading failed');
        }
      }
      
      // Give polyfill time to initialize
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    // Load squircle core with timeout
    console.log('🎨 Loading Squircle core...');
    const { init } = await import('@squircle/core');
    
    // Create initialization promise with timeout
    const initWithTimeout = Promise.race([
      init(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Initialization timeout after 15 seconds')), 15000)
      )
    ]);

    await initWithTimeout;
    console.log('✅ Squircle initialized successfully');
    
    return true;
    
  } catch (error) {
    console.error('❌ Squircle initialization failed:', error);
    
    // Provide detailed error analysis
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        console.error('💡 AbortError suggests worklet module loading issues. This could be due to:');
        console.error('   - CORS policy blocking worklet module');
        console.error('   - Build system not properly handling worklet files');
        console.error('   - Network issues preventing module download');
      } else if (error.message.includes('timeout')) {
        console.error('💡 Initialization timeout suggests:');
        console.error('   - Slow network connection');
        console.error('   - Build system bundling issues');
        console.error('   - Resource loading conflicts');
      } else if (error.message.includes('polyfill')) {
        console.error('💡 Polyfill loading failed - browser compatibility issue');
      }
    }
    
    return false;
  }
};

// Alternative polyfill loading via script tag
const loadPolyfillViaScript = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // This is a fallback - in practice, the module import should work
    // But we can try to load it differently if needed
    setTimeout(() => {
      reject(new Error('Script loading not implemented - using as fallback only'));
    }, 100);
  });
};

interface SquircleProviderProps {
  children: React.ReactNode;
  enableDebugLogs?: boolean;
  retryAttempts?: number;
  retryDelay?: number;
}

export function SquircleProviderV2({ 
  children, 
  enableDebugLogs = true,
  retryAttempts = 3,
  retryDelay = 1000
}: SquircleProviderProps) {
  const initRef = React.useRef(false);
  const [initStatus, setInitStatus] = React.useState<{
    status: 'pending' | 'success' | 'failed' | 'retrying';
    attempt: number;
    error?: string;
  }>({ status: 'pending', attempt: 0 });

  React.useEffect(() => {
    // Prevent double initialization
    if (initRef.current) return;
    initRef.current = true;

    let mounted = true;

    const attemptInitialization = async (attemptNumber: number): Promise<void> => {
      if (!mounted) return;

      if (enableDebugLogs) {
        console.log(`🎨 Squircle initialization attempt ${attemptNumber}/${retryAttempts}`);
      }

      setInitStatus({ status: attemptNumber > 1 ? 'retrying' : 'pending', attempt: attemptNumber });

      try {
        const success = await initSquircleWithFallbacks();
        
        if (mounted) {
          if (success) {
            setInitStatus({ status: 'success', attempt: attemptNumber });
          } else {
            throw new Error('Initialization returned false');
          }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        
        if (mounted) {
          if (attemptNumber < retryAttempts) {
            if (enableDebugLogs) {
              console.log(`🔄 Retrying in ${retryDelay}ms... (attempt ${attemptNumber + 1}/${retryAttempts})`);
            }
            
            setTimeout(() => {
              if (mounted) {
                attemptInitialization(attemptNumber + 1);
              }
            }, retryDelay);
          } else {
            setInitStatus({ 
              status: 'failed', 
              attempt: attemptNumber, 
              error: errorMessage 
            });
            
            if (enableDebugLogs) {
              console.error(`❌ All ${retryAttempts} initialization attempts failed`);
            }
          }
        }
      }
    };

    // Start initialization with multiple timing strategies
    const startInitialization = () => {
      // Strategy 1: Immediate if ready
      if (document.readyState === 'complete') {
        attemptInitialization(1);
        return;
      }

      // Strategy 2: On DOM ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          setTimeout(() => attemptInitialization(1), 100);
        }, { once: true });
        return;
      }

      // Strategy 3: On window load
      window.addEventListener('load', () => {
        setTimeout(() => attemptInitialization(1), 100);
      }, { once: true });
    };

    // Use requestIdleCallback for better performance
    if (typeof requestIdleCallback !== 'undefined') {
      requestIdleCallback(startInitialization, { timeout: 3000 });
    } else {
      setTimeout(startInitialization, 200);
    }

    return () => {
      mounted = false;
    };
  }, [retryAttempts, retryDelay, enableDebugLogs]);

  // Optional: Add visual indicator for debugging
  const showDebugInfo = enableDebugLogs && process.env.NODE_ENV === 'development';

  return (
    <>
      {showDebugInfo && (
        <div style={{
          position: 'fixed',
          top: 10,
          right: 10,
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 9999,
          fontFamily: 'monospace'
        }}>
          Squircle: {initStatus.status} (attempt {initStatus.attempt})
          {initStatus.error && <div style={{ color: '#ff6b6b' }}>Error: {initStatus.error}</div>}
        </div>
      )}
      {children}
    </>
  );
}

// Export both versions for flexibility
export { SquircleProviderV2 as SquircleProvider };