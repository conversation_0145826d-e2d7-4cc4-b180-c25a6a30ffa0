// components/waitlist/school.tsx
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface School {
  place_id: string;
  name: string;
  formatted_address: string;
}

interface WaitlistEmailProps {
  name: string;
  school?: School;
}

export const WaitlistEmail: React.FC<Readonly<WaitlistEmailProps>> = ({
  name,
  school,
}) => (
  <Html>
    <Head />
    <Preview>Welcome to the Penned School Waitlist - Institution-Wide Academic Integrity</Preview>
    <Body style={main as any}>
      <Container style={container as any}>
        <Heading style={h1 as any}>Welcome, School Leader! 🏫</Heading>
        <Text style={text as any}>
          Hi {name},
        </Text>
        <Text style={text as any}>
          Thank you for joining the Penned School waitlist{school ? ` representing ${school.name}` : ''}!
          We're developing comprehensive academic integrity solutions designed specifically for educational
          institutions that want to maintain trust and authenticity at scale.
        </Text>
        {school && (
          <Text style={schoolInfo as any}>
            <strong>School:</strong> {school.name}<br/>
            <strong>Address:</strong> {school.formatted_address}
          </Text>
        )}
        <Text style={text as any}>
          As a school administrator on our waitlist, you'll be among the first to:
        </Text>
        <Text style={text as any}>
          • Access institution-wide AI detection and monitoring<br/>
          • Get comprehensive analytics and reporting tools<br/>
          • Receive priority support for school-wide implementation<br/>
          • Join beta testing with other forward-thinking institutions
        </Text>
        <Text style={text as any}>
          We understand the unique challenges schools face in maintaining academic integrity
          while embracing technology. Our solution is built with institutional needs in mind.
        </Text>
        <Text style={text as any}>
          We'll keep you informed about our development progress and reach out when we're
          ready to discuss your school's specific needs.
        </Text>
        <Text style={textSmall as any}>
          Best regards,<br/>
          The Penned Team
        </Text>
      </Container>
    </Body>
  </Html>
);

export default WaitlistEmail;

const main = {
  backgroundColor: '#000000',
  margin: '0 auto',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
};

const container = {
  margin: 'auto',
  padding: '96px 20px 64px',
};

const h1 = {
  color: '#ffffff',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '40px',
  margin: '0 0 20px',
};

const text = {
  color: '#aaaaaa',
  fontSize: '14px',
  lineHeight: '24px',
  margin: '0 0 20px',
};

const textSmall = {
  color: '#888888',
  fontSize: '12px',
  lineHeight: '20px',
  margin: '20px 0 0',
};

const schoolInfo = {
  color: '#ffffff',
  fontSize: '14px',
  lineHeight: '24px',
  margin: '0 0 20px',
  padding: '16px',
  backgroundColor: '#1a1a1a',
  borderRadius: '8px',
  border: '1px solid #333333',
};