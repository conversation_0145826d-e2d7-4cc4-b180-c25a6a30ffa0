'use client';
import * as React from 'react';

// Alternative squircle implementation that bypasses worklet loading issues
const createSquircleCSS = () => {
  // Create CSS custom properties and fallback styles
  const style = document.createElement('style');
  style.textContent = `
    /* Squircle fallback using CSS */
    .squircle-fallback {
      position: relative;
      overflow: hidden;
    }
    
    .squircle-fallback::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--squircle-background-color, #3b82f6);
      border: var(--squircle-border-width, 0) solid var(--squircle-border-color, transparent);
      border-radius: var(--squircle-border-radius, 20px);
      z-index: -1;
    }
    
    /* Enhanced squircle using clip-path for better approximation */
    .squircle-enhanced {
      background: var(--squircle-background-color, #3b82f6);
      border: var(--squircle-border-width, 0) solid var(--squircle-border-color, transparent);
      clip-path: polygon(
        0% var(--squircle-border-radius, 20px),
        var(--squircle-border-radius, 20px) 0%,
        calc(100% - var(--squircle-border-radius, 20px)) 0%,
        100% var(--squircle-border-radius, 20px),
        100% calc(100% - var(--squircle-border-radius, 20px)),
        calc(100% - var(--squircle-border-radius, 20px)) 100%,
        var(--squircle-border-radius, 20px) 100%,
        0% calc(100% - var(--squircle-border-radius, 20px))
      );
    }
    
    /* Try to use paint worklet if available, fallback to enhanced */
    .squircle {
      background: paint(squircle);
    }
    
    /* If paint worklet fails, use enhanced fallback */
    @supports not (background: paint(squircle)) {
      .squircle {
        background: var(--squircle-background-color, #3b82f6) !important;
        border: var(--squircle-border-width, 0) solid var(--squircle-border-color, transparent);
        border-radius: calc(var(--squircle-border-radius, 20px) * 0.6);
      }
    }
  `;
  
  document.head.appendChild(style);
  console.log('✅ Squircle CSS fallback styles added');
};

// Simplified initialization that focuses on CSS fallbacks
const initSquircleWithFallback = async () => {
  if (typeof window === 'undefined') return;

  // Always add CSS fallbacks first
  createSquircleCSS();

  // Try to initialize the actual worklet, but don't fail if it doesn't work
  try {
    const hasNativeSupport = 'paintWorklet' in CSS;
    
    if (hasNativeSupport) {
      console.log('🎨 Attempting worklet initialization...');
      
      // Try a more direct approach to worklet loading
      const { init } = await import('@squircle/core');
      
      // Create a promise that resolves quickly if worklet fails
      const quickTimeout = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Quick timeout - using fallback')), 3000)
      );
      
      await Promise.race([init(), quickTimeout]);
      console.log('✅ Squircle worklet initialized successfully');
      return 'worklet';
      
    } else {
      console.log('🎨 No native support, loading polyfill...');
      await import('@squircle/paint-polyfill');
      
      const { init } = await import('@squircle/core');
      await Promise.race([
        init(), 
        new Promise((_, reject) => setTimeout(() => reject(new Error('Polyfill timeout')), 5000))
      ]);
      
      console.log('✅ Squircle polyfill initialized successfully');
      return 'polyfill';
    }
    
  } catch (error) {
    console.log('🎨 Worklet initialization failed, using CSS fallback');
    console.log('ℹ️ This is expected and the app will work with CSS-based rounded corners');
    return 'fallback';
  }
};

interface SquircleProviderProps {
  children: React.ReactNode;
  enableFallback?: boolean;
}

export function SquircleProviderFixed({ 
  children, 
  enableFallback = true 
}: SquircleProviderProps) {
  const [initStatus, setInitStatus] = React.useState<'pending' | 'worklet' | 'polyfill' | 'fallback'>('pending');
  const [isClient, setIsClient] = React.useState(false);
  const initRef = React.useRef(false);

  // Ensure we're on the client side to avoid hydration issues
  React.useEffect(() => {
    setIsClient(true);
  }, []);

  React.useEffect(() => {
    if (!isClient || initRef.current) return;
    initRef.current = true;

    let mounted = true;

    const initialize = async () => {
      if (!mounted) return;

      try {
        const result = await initSquircleWithFallback();
        if (mounted) {
          setInitStatus(result || 'fallback');
        }
      } catch (error) {
        console.log('🎨 Using CSS fallback for squircle shapes');
        if (mounted) {
          setInitStatus('fallback');
        }
      }
    };

    // Simple initialization - don't overcomplicate timing
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initialize, { once: true });
    } else {
      setTimeout(initialize, 100);
    }

    return () => {
      mounted = false;
    };
  }, [isClient]);

  return (
    <>
      {isClient && process.env.NODE_ENV === 'development' && (
        <div style={{
          position: 'fixed',
          bottom: 10,
          right: 10,
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '6px 10px',
          borderRadius: '4px',
          fontSize: '11px',
          zIndex: 9999,
          fontFamily: 'monospace'
        }}>
          Squircle: {initStatus}
        </div>
      )}
      {children}
    </>
  );
}

// Utility function to apply squircle styles programmatically
export const useSquircle = () => {
  return React.useCallback((element: HTMLElement | null, options: {
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: string;
    borderRadius?: string;
  } = {}) => {
    if (!element) return;

    // Apply CSS custom properties
    if (options.backgroundColor) {
      element.style.setProperty('--squircle-background-color', options.backgroundColor);
    }
    if (options.borderColor) {
      element.style.setProperty('--squircle-border-color', options.borderColor);
    }
    if (options.borderWidth) {
      element.style.setProperty('--squircle-border-width', options.borderWidth);
    }
    if (options.borderRadius) {
      element.style.setProperty('--squircle-border-radius', options.borderRadius);
    }

    // Add squircle class
    element.classList.add('squircle');
  }, []);
};

export default SquircleProviderFixed;