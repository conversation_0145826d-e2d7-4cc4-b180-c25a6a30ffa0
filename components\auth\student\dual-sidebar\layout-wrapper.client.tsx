'use client';
import type { ReactNode } from 'react';
import { EnhancedSidebarStudent } from './enhanced-sidebar-student.client';
import { SidebarProvider } from '../../../../stores/sidebar-store.client';

interface LayoutWrapperProps {
  children: ReactNode;
  showUserAvatar?: boolean;
  showTopNav?: boolean;
  enableRightSidebar?: boolean; // Keep option for future chat interface
  className?: string;
}

/**
 * LayoutWrapper - Student version similar to TanStack Start's _root.tsx
 * 
 * This component wraps the student application with the horizontal dual sidebar layout.
 * It provides the same functionality as the TanStack Start root component
 * but adapted for Blade framework and student-specific needs.
 * 
 * Features:
 * - Horizontal top navigation with logo, hamburger menu, and icon links
 * - Hamburger menu opens flyout from left side
 * - Icon buttons use Blade Link components for direct page navigation
 * - Simple student avatar with popover in top-right corner
 * - Full-width main content by default, adjusts when flyouts open
 * - Clean, writing-focused design for students
 * - Optional right sidebar support for future chat interface
 * 
 * Usage:
 * - Wrap your entire student app or specific routes with this component
 * - Similar to how TanStack Start uses _root.tsx to wrap all routes
 * - Provides consistent simplified layout across all student pages
 */
export function LayoutWrapper({
  children,
  showUserAvatar = true,
  showTopNav = true,
  enableRightSidebar = false,
  className
}: LayoutWrapperProps) {
  return (
    <SidebarProvider>
      <EnhancedSidebarStudent
        showUserAvatar={showUserAvatar}
        showTopNav={showTopNav}
        enableRightSidebar={enableRightSidebar}
      >
        {children}
      </EnhancedSidebarStudent>
    </SidebarProvider>
  );
}

/**
 * Alternative export for direct use as a layout component
 * This can be used in Blade's routing system or as a wrapper component
 */
export default LayoutWrapper;